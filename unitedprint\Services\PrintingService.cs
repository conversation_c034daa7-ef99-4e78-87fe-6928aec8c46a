using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Maui.Graphics;
using Microsoft.Maui.Graphics.Skia;
using ZXing;
using ZXing.Common;
using ZXing.QrCode;

#if WINDOWS
using System.Drawing;
using System.Drawing.Imaging;
// Zebra SDK references - uncomment when you have the SDK installed
// using Zebra.Sdk.Comm;
// using Zebra.Sdk.Printer;
// using Zebra.Sdk.Printer.Discovery;
#endif

namespace unitedprint.Services
{
    public class PrintingService
    {
        public async Task<string> PrintBadges(string studentId, string studentName, int attendeeCount)
        {
            try
            {
#if WINDOWS
                return await PrintBadgesWindows(studentId, studentName, attendeeCount);
#else
                return await Task.FromResult("Printing is only supported on Windows platform.");
#endif
            }
            catch (Exception ex)
            {
                return $"Printing failed: {ex.Message}";
            }
        }

#if WINDOWS
        private async Task<string> PrintBadgesWindows(string studentId, string studentName, int attendeeCount)
        {
            try
            {
                // For now, create badge images and save them to a folder
                // You can later integrate with actual Zebra printer when SDK is available

                string outputFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "PrintedBadges");
                if (!Directory.Exists(outputFolder))
                {
                    Directory.CreateDirectory(outputFolder);
                }

                // Generate QR code data
                string qrData = studentId;

                // Create badges
                for (int i = 1; i <= attendeeCount; i++)
                {
                    string badgePath = await CreateBadgeImage(qrData, studentName, i, attendeeCount);

                    // Move the badge to the output folder instead of printing
                    string finalPath = Path.Combine(outputFolder, $"Badge_{studentName}_{i}_{DateTime.Now:yyyyMMdd_HHmmss}.png");
                    File.Move(badgePath, finalPath);
                }

                return $"Badge images created successfully!\nSaved to: {outputFolder}\n\nNote: To enable actual printing, install the Zebra SDK and uncomment the printer code.";
            }
            catch (Exception ex)
            {
                return $"Badge creation error: {ex.Message}";
            }

            // Uncomment this section when you have Zebra SDK installed:
            /*
            string printerName = GetUSBPrinterName();

            if (string.IsNullOrEmpty(printerName))
            {
                // Try to discover Zebra printers
                try
                {
                    foreach (DiscoveredUsbPrinter usbPrinter in UsbDiscoverer.GetZebraUsbPrinters(new ZebraPrinterFilter()))
                    {
                        Console.WriteLine($"Found printer: {usbPrinter}");
                        printerName = usbPrinter.Address;
                        break; // Use the first found printer
                    }
                }
                catch (ConnectionException e)
                {
                    Console.WriteLine($"Error discovering printers: {e.Message}");
                    return $"Error discovering printers: {e.Message}";
                }
            }

            if (string.IsNullOrEmpty(printerName))
            {
                return "No USB printer found.";
            }

            try
            {
                Connection connection = new UsbConnection(printerName);
                connection.Open();

                ZebraPrinter printer = ZebraPrinterFactory.GetInstance(connection);

                // Generate QR code data
                string qrData = studentId;

                // Create badges
                for (int i = 1; i <= attendeeCount; i++)
                {
                    string badgePath = await CreateBadgeImage(qrData, studentName, i, attendeeCount);

                    // Print the badge
                    printer.PrintImage(badgePath, 133, 0);

                    // Clean up the temporary file
                    if (File.Exists(badgePath))
                    {
                        File.Delete(badgePath);
                    }
                }

                connection.Close();
                return "Printing completed successfully!";
            }
            catch (Exception ex)
            {
                return $"Printing error: {ex.Message}";
            }
            */
        }

        private async Task<string> CreateBadgeImage(string qrData, string studentName, int badgeNumber, int totalBadges)
        {
            const int badgeWidth = 480;
            const int badgeHeight = 400;

            // Create QR code
            var qrCodeImage = GenerateQRCode(qrData);
            
            // Create badge image
            using var badgeImage = new Bitmap(badgeWidth, badgeHeight);
            using var graphics = Graphics.FromImage(badgeImage);
            
            // Clear background
            graphics.Clear(System.Drawing.Color.White);

            // Draw title/header
            var topFont = new Font("Arial", 22, FontStyle.Bold);
            string headerText = "EDIS Fair 2024"; // You can customize this
            var topTextSize = graphics.MeasureString(headerText, topFont);
            var topTextLocation = new PointF((badgeWidth - topTextSize.Width) / 2, 15);
            graphics.DrawString(headerText, topFont, Brushes.Black, topTextLocation);

            // Draw QR code
            const int qrSize = 280;
            int qrX = (badgeWidth / 2) - (qrSize / 2) - 25;
            int qrY = 60;
            graphics.DrawImage(qrCodeImage, qrX, qrY, qrSize, qrSize);

            // Draw language circle (you can customize the language code)
            string languageCode = "EN"; // Default to English, you can make this configurable
            DrawLanguageCircle(graphics, qrX + qrSize + 20, qrY + (qrSize / 2), languageCode);

            // Draw bottom text (student name or "Visitor")
            string bottomText = badgeNumber == 1 ? studentName : "Visitor";
            var bottomFont = new Font("Arial", 22, FontStyle.Bold);
            var bottomTextSize = graphics.MeasureString(bottomText, bottomFont);
            int textY = qrY + qrSize + 20;
            var bottomTextLocation = new PointF((badgeWidth - bottomTextSize.Width) / 2, textY);
            graphics.DrawString(bottomText, bottomFont, Brushes.Black, bottomTextLocation);

            // Save to temporary file
            string tempPath = Path.Combine(Path.GetTempPath(), $"badge_{Guid.NewGuid():N}.png");
            badgeImage.Save(tempPath, ImageFormat.Png);

            return tempPath;
        }

        private void DrawLanguageCircle(Graphics graphics, int centerX, int centerY, string languageCode)
        {
            const int circleRadius = 50;
            
            // Draw circle border
            var pen = new Pen(Brushes.Black, 3);
            graphics.DrawEllipse(pen, centerX - circleRadius, centerY - circleRadius, circleRadius * 2, circleRadius * 2);
            
            // Fill circle background
            graphics.FillEllipse(Brushes.White, centerX - circleRadius + 1, centerY - circleRadius + 1, (circleRadius * 2) - 2, (circleRadius * 2) - 2);
            
            // Draw language code text
            var languageFont = new Font("Arial", 24, FontStyle.Bold);
            var codeSize = graphics.MeasureString(languageCode, languageFont);
            var codeLocation = new PointF(centerX - codeSize.Width / 2, centerY - codeSize.Height / 2);
            graphics.DrawString(languageCode, languageFont, Brushes.Black, codeLocation);
        }

        private Bitmap GenerateQRCode(string qrData)
        {
            var barcodeWriter = new BarcodeWriter<Bitmap>
            {
                Format = BarcodeFormat.QR_CODE,
                Options = new EncodingOptions
                {
                    Width = 280,
                    Height = 280,
                    Margin = 0
                }
            };

            return barcodeWriter.Write(qrData);
        }

        private string GetUSBPrinterName()
        {
            try
            {
                var printDoc = new System.Drawing.Printing.PrintDocument();
                return printDoc.PrinterSettings.PrinterName;
            }
            catch
            {
                return string.Empty;
            }
        }
#endif
    }
}
