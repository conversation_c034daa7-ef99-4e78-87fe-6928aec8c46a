@echo off
echo Copying and renaming images from source folder to project...

set SOURCE_FOLDER=C:\Users\<USER>\Desktop\unitedagent\unitededisprinteryurdisi\unitededisprinter\images
set DEST_FOLDER=unitedprint\Resources\Images

echo Source: %SOURCE_FOLDER%
echo Destination: %DEST_FOLDER%

if not exist "%SOURCE_FOLDER%" (
    echo ERROR: Source folder does not exist!
    echo Please check the path: %SOURCE_FOLDER%
    pause
    exit /b 1
)

if not exist "%DEST_FOLDER%" (
    echo Creating destination folder...
    mkdir "%DEST_FOLDER%"
)

echo Copying and renaming images with MAUI-compliant names...

if exist "%SOURCE_FOLDER%\2-03.png" copy "%SOURCE_FOLDER%\2-03.png" "%DEST_FOLDER%\search_logo.png"
if exist "%SOURCE_FOLDER%\2-04.png" copy "%SOURCE_FOLDER%\2-04.png" "%DEST_FOLDER%\logo_two.png"
if exist "%SOURCE_FOLDER%\2-05.png" copy "%SOURCE_FOLDER%\2-05.png" "%DEST_FOLDER%\logo_three.png"
if exist "%SOURCE_FOLDER%\EDIS-e-LOGO.png" copy "%SOURCE_FOLDER%\EDIS-e-LOGO.png" "%DEST_FOLDER%\edis_logo.png"
if exist "%SOURCE_FOLDER%\yn-03.png" copy "%SOURCE_FOLDER%\yn-03.png" "%DEST_FOLDER%\edis_small_logo.png"
if exist "%SOURCE_FOLDER%\za-02.png" copy "%SOURCE_FOLDER%\za-02.png" "%DEST_FOLDER%\eia_logo.png"
if exist "%SOURCE_FOLDER%\za-02 - Copy.png" copy "%SOURCE_FOLDER%\za-02 - Copy.png" "%DEST_FOLDER%\eia_logo_copy.png"
if exist "%SOURCE_FOLDER%\1.png" copy "%SOURCE_FOLDER%\1.png" "%DEST_FOLDER%\image_one.png"
if exist "%SOURCE_FOLDER%\4.png" copy "%SOURCE_FOLDER%\4.png" "%DEST_FOLDER%\image_four.png"
if exist "%SOURCE_FOLDER%\5.png" copy "%SOURCE_FOLDER%\5.png" "%DEST_FOLDER%\image_five.png"
if exist "%SOURCE_FOLDER%\500.jpg" copy "%SOURCE_FOLDER%\500.jpg" "%DEST_FOLDER%\image_fivehundred.jpg"

echo.
echo Images copied and renamed successfully!
echo.
echo MAUI-compliant file names used:
echo - 2-03.png → search_logo.png (search logo above textbox)
echo - EDIS-e-LOGO.png → edis_logo.png (main EDIS logo)
echo - yn-03.png → edis_small_logo.png (small EDIS logo)
echo - za-02.png → eia_logo.png (EIA logo)
echo - Other files renamed with compliant names
echo.
pause
