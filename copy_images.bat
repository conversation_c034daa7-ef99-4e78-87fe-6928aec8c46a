@echo off
echo Copying images from source folder to project...

set SOURCE_FOLDER=C:\Users\<USER>\Desktop\unitedagent\unitededisprinteryurdisi\unitededisprinter\images
set DEST_FOLDER=unitedprint\Resources\Images

echo Source: %SOURCE_FOLDER%
echo Destination: %DEST_FOLDER%

if not exist "%SOURCE_FOLDER%" (
    echo ERROR: Source folder does not exist!
    echo Please check the path: %SOURCE_FOLDER%
    pause
    exit /b 1
)

if not exist "%DEST_FOLDER%" (
    echo Creating destination folder...
    mkdir "%DEST_FOLDER%"
)

echo Copying all images...
copy "%SOURCE_FOLDER%\*.*" "%DEST_FOLDER%\"

echo.
echo Images copied successfully!
echo.
echo Please rename the images to match the following names:
echo - Main EDIS logo: edis_logo.png
echo - Small EDIS logo: edis_small_logo.png
echo - EIA logo: eia_logo.png
echo - Search logo (2-03.png): search_logo.png
echo.
pause
