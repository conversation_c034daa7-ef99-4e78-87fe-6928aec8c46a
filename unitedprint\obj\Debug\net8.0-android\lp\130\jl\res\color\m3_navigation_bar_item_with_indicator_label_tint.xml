<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright 2021 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<selector xmlns:android="http://schemas.android.com/apk/res/android">
  <!-- Active item -->
  <item android:color="@macro/m3_comp_navigation_bar_active_pressed_label_text_color"
        android:state_pressed="true" android:state_checked="true"/>
  <item android:color="@macro/m3_comp_navigation_bar_active_focus_label_text_color"
        android:state_focused="true" android:state_checked="true"/>
  <item android:color="@macro/m3_comp_navigation_bar_active_hover_label_text_color"
        android:state_hovered="true" android:state_checked="true"/>
  <item android:color="@macro/m3_comp_navigation_bar_active_label_text_color"
        android:state_checked="true"/>

  <!-- Inactive item -->
  <item android:color="@macro/m3_comp_navigation_bar_inactive_pressed_label_text_color"
        android:state_pressed="true"/>
  <item android:color="@macro/m3_comp_navigation_bar_inactive_focus_label_text_color"
        android:state_focused="true"/>
  <item android:color="@macro/m3_comp_navigation_bar_inactive_hover_label_text_color"
        android:state_hovered="true"/>
  <item android:color="@macro/m3_comp_navigation_bar_inactive_label_text_color"/>
</selector>
