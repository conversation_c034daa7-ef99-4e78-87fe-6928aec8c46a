# Image Setup Instructions

## Steps to add images to your MAUI project:

### 1. Copy Images
Run the `copy_images.bat` file to copy images from your source folder to the project.

Or manually copy images from:
```
C:\Users\<USER>\Desktop\unitedagent\unitededisprinteryurdisi\unitededisprinter\images
```

To:
```
unitedprint\Resources\Images\
```

### 2. Rename Images
Make sure the images have these exact names:
- **edis_logo.png** - Main EDIS logo (will be displayed at the top)
- **edis_small_logo.png** - Small EDIS logo (bottom left)
- **eia_logo.png** - EIA logo (bottom right)

### 3. Supported Image Formats
- PNG (recommended)
- JPG/JPEG
- SVG
- GIF

### 4. Recommended Image Sizes
- **edis_logo.png**: 300x120 pixels or similar aspect ratio
- **edis_small_logo.png**: 60x60 pixels (square)
- **eia_logo.png**: 60x60 pixels (square)

### 5. Build and Run
After adding the images:
```bash
dotnet build
dotnet run --framework net9.0-windows10.0.19041.0
```

## Current Design Features:
- ✅ Gradient background (green to purple)
- ✅ EDIS logo at top (now uses image)
- ✅ Input field for QR Code/Mobile Number
- ✅ Numeric keypad (0-9, Reset, Delete)
- ✅ Search button
- ✅ Bottom logos (now uses images)
- ✅ Responsive layout

## If Images Don't Show:
1. Make sure image files are in `unitedprint\Resources\Images\`
2. Check that image names match exactly (case-sensitive)
3. Rebuild the project: `dotnet clean` then `dotnet build`
4. Check that images are included in the .csproj file (already done)
