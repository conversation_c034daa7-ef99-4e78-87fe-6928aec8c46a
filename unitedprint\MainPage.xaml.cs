using Microsoft.Maui.Controls;

namespace unitedprint
{
    public partial class MainPage : ContentPage
    {
        public MainPage()
        {
            InitializeComponent();
        }

        private void OnNumberClicked(object sender, EventArgs e)
        {
            if (sender is Button button)
            {
                InputEntry.Text += button.Text;
            }
        }

        private void OnResetClicked(object sender, EventArgs e)
        {
            InputEntry.Text = string.Empty;
        }

        private void OnDeleteClicked(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(InputEntry.Text))
            {
                InputEntry.Text = InputEntry.Text.Substring(0, InputEntry.Text.Length - 1);
            }
        }

        private async void OnSearchClicked(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(InputEntry.Text))
            {
                await DisplayAlert("Error", "Please enter a QR Code or Mobile Number", "OK");
                return;
            }

            // Here you can add your search logic
            await Display<PERSON>lert("Search", $"Searching for: {InputEntry.Text}", "OK");
        }
    }

}
