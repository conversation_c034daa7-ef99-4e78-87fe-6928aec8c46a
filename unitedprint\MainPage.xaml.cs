﻿using Microsoft.Maui.Controls;
using System.Text.Json;

namespace unitedprint
{
    public partial class MainPage : ContentPage
    {
        private readonly HttpClient _httpClient;
        private const string API_BASE_URL = "https://acceptedu.com/api/fair-registrations/search";
        private const string API_TOKEN = "accept_fair_api_token_2024";

        public MainPage()
        {
            InitializeComponent();
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {API_TOKEN}");
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
        }

        private void OnNumberClicked(object sender, EventArgs e)
        {
            if (sender is Button button)
            {
                InputEntry.Text += button.Text;
            }
        }

        private void OnResetClicked(object sender, EventArgs e)
        {
            InputEntry.Text = string.Empty;
        }

        private void OnDeleteClicked(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(InputEntry.Text))
            {
                InputEntry.Text = InputEntry.Text.Substring(0, InputEntry.Text.Length - 1);
            }
        }

        private async void OnEntryCompleted(object sender, EventArgs e)
        {
            // This is called when user presses Enter
            await PerformSearch();
        }

        private async void OnSearchClicked(object sender, EventArgs e)
        {
            await PerformSearch();
        }

        private async Task PerformSearch()
        {
            if (string.IsNullOrWhiteSpace(InputEntry.Text))
            {
                await DisplayAlert("Error", "Please enter a QR Code or Mobile Number", "OK");
                return;
            }

            try
            {
                // Show loading overlay
                ShowLoadingOverlay();

                // Make API call
                var response = await SearchRegistration(InputEntry.Text);

                // Hide loading overlay
                HideLoadingOverlay();

                // Handle response
                if (response.HasValue)
                {
                    // Handle the API response with multiple results
                    await HandleSearchResults(response.Value, InputEntry.Text);
                }
                else
                {
                    await DisplayAlert("No Results", "No registration found for this number.", "OK");
                }
            }
            catch (Exception ex)
            {
                // Make sure to hide loading overlay on error
                HideLoadingOverlay();
                await DisplayAlert("Error", $"Search failed: {ex.Message}", "OK");
            }
        }

        private Frame _loadingOverlay;

        private void ShowLoadingOverlay()
        {
            // Remove any existing overlay first
            HideLoadingOverlay();

            // Create loading overlay
            _loadingOverlay = new Frame
            {
                BackgroundColor = Color.FromArgb("#80000000"), // Semi-transparent black
                CornerRadius = 15,
                Padding = new Thickness(30, 20),
                HorizontalOptions = LayoutOptions.Center,
                VerticalOptions = LayoutOptions.Center
            };

            var stackLayout = new StackLayout
            {
                Orientation = StackOrientation.Vertical,
                Spacing = 15,
                HorizontalOptions = LayoutOptions.Center
            };

            var titleLabel = new Label
            {
                Text = "Searching",
                FontSize = 20,
                FontAttributes = FontAttributes.Bold,
                TextColor = Colors.White,
                HorizontalOptions = LayoutOptions.Center
            };

            var messageLabel = new Label
            {
                Text = "Please wait...",
                FontSize = 16,
                TextColor = Colors.White,
                HorizontalOptions = LayoutOptions.Center
            };

            stackLayout.Children.Add(titleLabel);
            stackLayout.Children.Add(messageLabel);
            _loadingOverlay.Content = stackLayout;

            // Add to main grid
            MainGrid.Children.Add(_loadingOverlay);
        }

        private void HideLoadingOverlay()
        {
            // Remove the loading overlay if it exists
            if (_loadingOverlay != null && MainGrid.Children.Contains(_loadingOverlay))
            {
                MainGrid.Children.Remove(_loadingOverlay);
                _loadingOverlay = null;
            }
        }

        private async Task<JsonElement?> SearchRegistration(string phoneNumber)
        {
            try
            {
                var url = $"{API_BASE_URL}?phone={phoneNumber}";
                var response = await _httpClient.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    var jsonString = await response.Content.ReadAsStringAsync();
                    var jsonDocument = JsonDocument.Parse(jsonString);
                    return jsonDocument.RootElement;
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return null; // No registration found
                }
                else
                {
                    throw new Exception($"API Error: {response.StatusCode} - {response.ReasonPhrase}");
                }
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Network error: {ex.Message}");
            }
            catch (JsonException ex)
            {
                throw new Exception($"Invalid response format: {ex.Message}");
            }
        }

        private async Task HandleSearchResults(JsonElement response, string phoneNumber)
        {
            try
            {
                // Check if response has the expected structure
                if (!response.TryGetProperty("status", out var statusElement) ||
                    statusElement.GetString() != "success")
                {
                    await DisplayAlert("Error", "Invalid response from server", "OK");
                    return;
                }

                if (!response.TryGetProperty("data", out var dataElement) ||
                    dataElement.ValueKind != JsonValueKind.Array)
                {
                    await DisplayAlert("Error", "No data found in response", "OK");
                    return;
                }

                var results = dataElement.EnumerateArray().ToList();

                if (results.Count == 0)
                {
                    await DisplayAlert("No Results", "No registration found for this number.", "OK");
                    return;
                }

                if (results.Count == 1)
                {
                    // Single result - navigate directly
                    await NavigateToResults(results[0], phoneNumber);
                }
                else
                {
                    // Multiple results - show selection popup
                    await ShowStudentSelectionPopup(results, phoneNumber);
                }
            }
            catch (Exception ex)
            {
                await DisplayAlert("Error", $"Failed to process search results: {ex.Message}", "OK");
            }
        }

        private async Task ShowStudentSelectionPopup(List<JsonElement> students, string phoneNumber)
        {
            try
            {
                // Navigate to custom student selection page
                var selectionPage = new StudentSelectionPage(students, phoneNumber);
                await Shell.Current.Navigation.PushAsync(selectionPage);
            }
            catch (Exception ex)
            {
                await DisplayAlert("Error", $"Failed to show student selection: {ex.Message}", "OK");
            }
        }

        private async Task NavigateToResults(JsonElement result, string phoneNumber)
        {
            try
            {
                // Navigate to the results page with the registration data
                var resultsPage = new ResultsPage(result, phoneNumber);
                await Shell.Current.Navigation.PushAsync(resultsPage);
            }
            catch (Exception ex)
            {
                await DisplayAlert("Error", $"Failed to navigate to results: {ex.Message}", "OK");
            }
        }

        private async Task DisplaySearchResults(JsonElement result)
        {
            try
            {
                // Extract relevant information from the JSON response
                var resultText = "Registration Found:\n\n";

                // Add fields from the API response structure (underscore format)
                if (result.TryGetProperty("id", out var id))
                    resultText += $"ID: {id.GetInt32()}\n";

                if (result.TryGetProperty("student_name", out var studentName))
                    resultText += $"Student: {studentName.GetString()}\n";

                if (result.TryGetProperty("student_phone", out var studentPhone))
                    resultText += $"Phone: {studentPhone.GetString()}\n";

                if (result.TryGetProperty("student_email", out var studentEmail))
                    resultText += $"Email: {studentEmail.GetString()}\n";

                if (result.TryGetProperty("residence_city", out var city))
                    resultText += $"City: {city.GetString()}\n";

                if (result.TryGetProperty("desired_major", out var major))
                    resultText += $"Major: {major.GetString()}\n";

                if (result.TryGetProperty("education_level", out var education))
                    resultText += $"Education: {education.GetString()}\n";

                // If no specific fields found, show the raw JSON (formatted)
                if (resultText == "Registration Found:\n\n")
                {
                    resultText += JsonSerializer.Serialize(result, new JsonSerializerOptions
                    {
                        WriteIndented = true
                    });
                }

                await DisplayAlert("Search Results", resultText, "OK");
            }
            catch (Exception ex)
            {
                await DisplayAlert("Error", $"Failed to display results: {ex.Message}", "OK");
            }
        }

        protected override void OnDisappearing()
        {
            base.OnDisappearing();
            _httpClient?.Dispose();
        }
    }

}
