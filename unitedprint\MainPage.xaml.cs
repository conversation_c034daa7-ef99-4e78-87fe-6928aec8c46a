﻿using Microsoft.Maui.Controls;
using System.Text.Json;

namespace unitedprint
{
    public partial class MainPage : ContentPage
    {
        private readonly HttpClient _httpClient;
        private const string API_BASE_URL = "https://acceptedu.com/api/fair-registrations/search";
        private const string API_TOKEN = "accept_fair_api_token_2024";

        public MainPage()
        {
            InitializeComponent();
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {API_TOKEN}");
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
        }

        private void OnNumberClicked(object sender, EventArgs e)
        {
            if (sender is Button button)
            {
                InputEntry.Text += button.Text;
            }
        }

        private void OnResetClicked(object sender, EventArgs e)
        {
            InputEntry.Text = string.Empty;
        }

        private void OnDeleteClicked(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(InputEntry.Text))
            {
                InputEntry.Text = InputEntry.Text.Substring(0, InputEntry.Text.Length - 1);
            }
        }

        private async void OnSearchClicked(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(InputEntry.Text))
            {
                await DisplayAlert("Error", "Please enter a QR Code or Mobile Number", "OK");
                return;
            }

            try
            {
                // Show loading indicator
                var loadingAlert = DisplayAlert("Searching", "Please wait...", "Cancel");

                // Make API call
                var response = await SearchRegistration(InputEntry.Text);

                // Cancel loading alert
                // Note: In a real app, you'd want to use a proper loading indicator

                if (response.HasValue)
                {
                    // Parse and display results
                    await DisplaySearchResults(response.Value);
                }
                else
                {
                    await DisplayAlert("No Results", "No registration found for this number.", "OK");
                }
            }
            catch (Exception ex)
            {
                await DisplayAlert("Error", $"Search failed: {ex.Message}", "OK");
            }
        }

        private async Task<JsonElement?> SearchRegistration(string phoneNumber)
        {
            try
            {
                var url = $"{API_BASE_URL}?phone={phoneNumber}";
                var response = await _httpClient.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    var jsonString = await response.Content.ReadAsStringAsync();
                    var jsonDocument = JsonDocument.Parse(jsonString);
                    return jsonDocument.RootElement;
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return null; // No registration found
                }
                else
                {
                    throw new Exception($"API Error: {response.StatusCode} - {response.ReasonPhrase}");
                }
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Network error: {ex.Message}");
            }
            catch (JsonException ex)
            {
                throw new Exception($"Invalid response format: {ex.Message}");
            }
        }

        private async Task DisplaySearchResults(JsonElement result)
        {
            try
            {
                // Extract relevant information from the JSON response
                var resultText = "Registration Found:\n\n";

                // Add common fields that might be in the response
                if (result.TryGetProperty("name", out var name))
                    resultText += $"Name: {name.GetString()}\n";

                if (result.TryGetProperty("phone", out var phone))
                    resultText += $"Phone: {phone.GetString()}\n";

                if (result.TryGetProperty("email", out var email))
                    resultText += $"Email: {email.GetString()}\n";

                if (result.TryGetProperty("registration_id", out var regId))
                    resultText += $"Registration ID: {regId.GetString()}\n";

                if (result.TryGetProperty("status", out var status))
                    resultText += $"Status: {status.GetString()}\n";

                // If no specific fields found, show the raw JSON (formatted)
                if (resultText == "Registration Found:\n\n")
                {
                    resultText += JsonSerializer.Serialize(result, new JsonSerializerOptions
                    {
                        WriteIndented = true
                    });
                }

                await DisplayAlert("Search Results", resultText, "OK");
            }
            catch (Exception ex)
            {
                await DisplayAlert("Error", $"Failed to display results: {ex.Message}", "OK");
            }
        }

        protected override void OnDisappearing()
        {
            base.OnDisappearing();
            _httpClient?.Dispose();
        }
    }

}
