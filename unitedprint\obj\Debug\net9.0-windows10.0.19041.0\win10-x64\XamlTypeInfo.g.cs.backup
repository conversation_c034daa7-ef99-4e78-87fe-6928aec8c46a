﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.Diagnostics.CodeAnalysis;


namespace unitedprint.WinUI
{
    public partial class App : global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider
    {
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2409")]
        private global::unitedprint.unitedprint_XamlTypeInfo.XamlMetaDataProvider __appProvider;

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2409")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private global::unitedprint.unitedprint_XamlTypeInfo.XamlMetaDataProvider _AppProvider
        {
            get
            {
                if (__appProvider == null)
                {
                    __appProvider = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMetaDataProvider();
                }
                return __appProvider;
            }
        }

        /// <summary>
        /// GetXamlType(Type)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2409")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IXamlType GetXamlType(global::System.Type type)
        {
            return _AppProvider.GetXamlType(type);
        }

        /// <summary>
        /// GetXamlType(String)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2409")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IXamlType GetXamlType(string fullName)
        {
            return _AppProvider.GetXamlType(fullName);
        }

        /// <summary>
        /// GetXmlnsDefinitions()
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2409")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.XmlnsDefinition[] GetXmlnsDefinitions()
        {
            return _AppProvider.GetXmlnsDefinitions();
        }
    }
}

namespace unitedprint.unitedprint_XamlTypeInfo
{
    /// <summary>
    /// Main class for providing metadata for the app or library
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2409")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    public sealed partial class XamlMetaDataProvider : global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider
    {
        private global::unitedprint.unitedprint_XamlTypeInfo.XamlTypeInfoProvider _provider = null;

        private global::unitedprint.unitedprint_XamlTypeInfo.XamlTypeInfoProvider Provider
        {
            get
            {
                if (_provider == null)
                {
                    _provider = new global::unitedprint.unitedprint_XamlTypeInfo.XamlTypeInfoProvider();
                }
                return _provider;
            }
        }

        /// <summary>
        /// GetXamlType(Type)
        /// </summary>
        [global::Windows.Foundation.Metadata.DefaultOverload]
        public global::Microsoft.UI.Xaml.Markup.IXamlType GetXamlType(global::System.Type type)
        {
            return Provider.GetXamlTypeByType(type);
        }

        /// <summary>
        /// GetXamlType(String)
        /// </summary>
        public global::Microsoft.UI.Xaml.Markup.IXamlType GetXamlType(string fullName)
        {
            return Provider.GetXamlTypeByName(fullName);
        }

        /// <summary>
        /// GetXmlnsDefinitions()
        /// </summary>
        public global::Microsoft.UI.Xaml.Markup.XmlnsDefinition[] GetXmlnsDefinitions()
        {
            return new global::Microsoft.UI.Xaml.Markup.XmlnsDefinition[0];
        }
    }

    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2409")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    internal partial class XamlTypeInfoProvider
    {
        public global::Microsoft.UI.Xaml.Markup.IXamlType GetXamlTypeByType(global::System.Type type)
        {
            global::Microsoft.UI.Xaml.Markup.IXamlType xamlType;
            lock (_xamlTypeCacheByType) 
            { 
                if (_xamlTypeCacheByType.TryGetValue(type, out xamlType))
                {
                    return xamlType;
                }
                int typeIndex = LookupTypeIndexByType(type);
                if(typeIndex != -1)
                {
                    xamlType = CreateXamlType(typeIndex);
                }
                var userXamlType = xamlType as global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType;
                if(xamlType == null || (userXamlType != null && userXamlType.IsReturnTypeStub && !userXamlType.IsLocalType))
                {
                    global::Microsoft.UI.Xaml.Markup.IXamlType libXamlType = CheckOtherMetadataProvidersForType(type);
                    if (libXamlType != null)
                    {
                        if(libXamlType.IsConstructible || xamlType == null)
                        {
                            xamlType = libXamlType;
                        }
                    }
                }
                if (xamlType != null)
                {
                    _xamlTypeCacheByName.Add(xamlType.FullName, xamlType);
                    _xamlTypeCacheByType.Add(xamlType.UnderlyingType, xamlType);
                }
            }
            return xamlType;
        }

        public global::Microsoft.UI.Xaml.Markup.IXamlType GetXamlTypeByName(string typeName)
        {
            if (string.IsNullOrEmpty(typeName))
            {
                return null;
            }
            global::Microsoft.UI.Xaml.Markup.IXamlType xamlType;
            lock (_xamlTypeCacheByType)
            {
                if (_xamlTypeCacheByName.TryGetValue(typeName, out xamlType))
                {
                    return xamlType;
                }
                int typeIndex = LookupTypeIndexByName(typeName);
                if(typeIndex != -1)
                {
                    xamlType = CreateXamlType(typeIndex);
                }
                var userXamlType = xamlType as global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType;
                if(xamlType == null || (userXamlType != null && userXamlType.IsReturnTypeStub && !userXamlType.IsLocalType))
                {
                    global::Microsoft.UI.Xaml.Markup.IXamlType libXamlType = CheckOtherMetadataProvidersForName(typeName);
                    if (libXamlType != null)
                    {
                        if(libXamlType.IsConstructible || xamlType == null)
                        {
                            xamlType = libXamlType;
                        }
                    }
                }
                if (xamlType != null)
                {
                    _xamlTypeCacheByName.Add(xamlType.FullName, xamlType);
                    _xamlTypeCacheByType.Add(xamlType.UnderlyingType, xamlType);
                }
            }
            return xamlType;
        }

        public global::Microsoft.UI.Xaml.Markup.IXamlMember GetMemberByLongName(string longMemberName)
        {
            if (string.IsNullOrEmpty(longMemberName))
            {
                return null;
            }
            global::Microsoft.UI.Xaml.Markup.IXamlMember xamlMember;
            lock (_xamlMembers)
            {
                if (_xamlMembers.TryGetValue(longMemberName, out xamlMember))
                {
                    return xamlMember;
                }
                xamlMember = CreateXamlMember(longMemberName);
                if (xamlMember != null)
                {
                    _xamlMembers.Add(longMemberName, xamlMember);
                }
            }
            return xamlMember;
        }

        global::System.Collections.Generic.Dictionary<string, global::Microsoft.UI.Xaml.Markup.IXamlType>
                _xamlTypeCacheByName = new global::System.Collections.Generic.Dictionary<string, global::Microsoft.UI.Xaml.Markup.IXamlType>();

        global::System.Collections.Generic.Dictionary<global::System.Type, global::Microsoft.UI.Xaml.Markup.IXamlType>
                _xamlTypeCacheByType = new global::System.Collections.Generic.Dictionary<global::System.Type, global::Microsoft.UI.Xaml.Markup.IXamlType>();

        global::System.Collections.Generic.Dictionary<string, global::Microsoft.UI.Xaml.Markup.IXamlMember>
                _xamlMembers = new global::System.Collections.Generic.Dictionary<string, global::Microsoft.UI.Xaml.Markup.IXamlMember>();

        string[] _typeNameTable = null;
        global::System.Type[] _typeTable = null;
        
        private void InitTypeTables()
        {
            _typeNameTable = new string[35];
            _typeNameTable[0] = "Microsoft.Maui.MauiWinUIApplication";
            _typeNameTable[1] = "Microsoft.UI.Xaml.Application";
            _typeNameTable[2] = "Microsoft.UI.Xaml.LaunchActivatedEventArgs";
            _typeNameTable[3] = "System.IServiceProvider";
            _typeNameTable[4] = "Microsoft.Maui.IApplication";
            _typeNameTable[5] = "Microsoft.Maui.Controls.Platform.ShellView";
            _typeNameTable[6] = "Microsoft.Maui.Platform.RootNavigationView";
            _typeNameTable[7] = "Microsoft.Maui.Platform.MauiNavigationView";
            _typeNameTable[8] = "Microsoft.UI.Xaml.Controls.NavigationView";
            _typeNameTable[9] = "Microsoft.UI.Xaml.Controls.ContentControl";
            _typeNameTable[10] = "Microsoft.UI.Xaml.Thickness";
            _typeNameTable[11] = "System.ValueType";
            _typeNameTable[12] = "Object";
            _typeNameTable[13] = "Boolean";
            _typeNameTable[14] = "Microsoft.UI.Xaml.Controls.AutoSuggestBox";
            _typeNameTable[15] = "Double";
            _typeNameTable[16] = "Microsoft.UI.Xaml.UIElement";
            _typeNameTable[17] = "Microsoft.UI.Xaml.Controls.NavigationViewDisplayMode";
            _typeNameTable[18] = "System.Enum";
            _typeNameTable[19] = "System.Collections.Generic.IList`1<Object>";
            _typeNameTable[20] = "Microsoft.UI.Xaml.DataTemplate";
            _typeNameTable[21] = "Microsoft.UI.Xaml.Controls.NavigationViewBackButtonVisible";
            _typeNameTable[22] = "Microsoft.UI.Xaml.Style";
            _typeNameTable[23] = "Microsoft.UI.Xaml.Controls.StyleSelector";
            _typeNameTable[24] = "Microsoft.UI.Xaml.Controls.DataTemplateSelector";
            _typeNameTable[25] = "Microsoft.UI.Xaml.Controls.NavigationViewOverflowLabelMode";
            _typeNameTable[26] = "Microsoft.UI.Xaml.Controls.NavigationViewPaneDisplayMode";
            _typeNameTable[27] = "String";
            _typeNameTable[28] = "Microsoft.UI.Xaml.Controls.NavigationViewSelectionFollowsFocus";
            _typeNameTable[29] = "Microsoft.UI.Xaml.Controls.NavigationViewShoulderNavigationEnabled";
            _typeNameTable[30] = "Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings";
            _typeNameTable[31] = "Microsoft.UI.Xaml.DependencyObject";
            _typeNameTable[32] = "Microsoft.UI.Xaml.Controls.TreeViewNode";
            _typeNameTable[33] = "System.Collections.Generic.IList`1<Microsoft.UI.Xaml.Controls.TreeViewNode>";
            _typeNameTable[34] = "Int32";

            _typeTable = new global::System.Type[35];
            _typeTable[0] = typeof(global::Microsoft.Maui.MauiWinUIApplication);
            _typeTable[1] = typeof(global::Microsoft.UI.Xaml.Application);
            _typeTable[2] = typeof(global::Microsoft.UI.Xaml.LaunchActivatedEventArgs);
            _typeTable[3] = typeof(global::System.IServiceProvider);
            _typeTable[4] = typeof(global::Microsoft.Maui.IApplication);
            _typeTable[5] = typeof(global::Microsoft.Maui.Controls.Platform.ShellView);
            _typeTable[6] = typeof(global::Microsoft.Maui.Platform.RootNavigationView);
            _typeTable[7] = typeof(global::Microsoft.Maui.Platform.MauiNavigationView);
            _typeTable[8] = typeof(global::Microsoft.UI.Xaml.Controls.NavigationView);
            _typeTable[9] = typeof(global::Microsoft.UI.Xaml.Controls.ContentControl);
            _typeTable[10] = typeof(global::Microsoft.UI.Xaml.Thickness);
            _typeTable[11] = typeof(global::System.ValueType);
            _typeTable[12] = typeof(global::System.Object);
            _typeTable[13] = typeof(global::System.Boolean);
            _typeTable[14] = typeof(global::Microsoft.UI.Xaml.Controls.AutoSuggestBox);
            _typeTable[15] = typeof(global::System.Double);
            _typeTable[16] = typeof(global::Microsoft.UI.Xaml.UIElement);
            _typeTable[17] = typeof(global::Microsoft.UI.Xaml.Controls.NavigationViewDisplayMode);
            _typeTable[18] = typeof(global::System.Enum);
            _typeTable[19] = typeof(global::System.Collections.Generic.IList<global::System.Object>);
            _typeTable[20] = typeof(global::Microsoft.UI.Xaml.DataTemplate);
            _typeTable[21] = typeof(global::Microsoft.UI.Xaml.Controls.NavigationViewBackButtonVisible);
            _typeTable[22] = typeof(global::Microsoft.UI.Xaml.Style);
            _typeTable[23] = typeof(global::Microsoft.UI.Xaml.Controls.StyleSelector);
            _typeTable[24] = typeof(global::Microsoft.UI.Xaml.Controls.DataTemplateSelector);
            _typeTable[25] = typeof(global::Microsoft.UI.Xaml.Controls.NavigationViewOverflowLabelMode);
            _typeTable[26] = typeof(global::Microsoft.UI.Xaml.Controls.NavigationViewPaneDisplayMode);
            _typeTable[27] = typeof(global::System.String);
            _typeTable[28] = typeof(global::Microsoft.UI.Xaml.Controls.NavigationViewSelectionFollowsFocus);
            _typeTable[29] = typeof(global::Microsoft.UI.Xaml.Controls.NavigationViewShoulderNavigationEnabled);
            _typeTable[30] = typeof(global::Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings);
            _typeTable[31] = typeof(global::Microsoft.UI.Xaml.DependencyObject);
            _typeTable[32] = typeof(global::Microsoft.UI.Xaml.Controls.TreeViewNode);
            _typeTable[33] = typeof(global::System.Collections.Generic.IList<global::Microsoft.UI.Xaml.Controls.TreeViewNode>);
            _typeTable[34] = typeof(global::System.Int32);
        }

        private int LookupTypeIndexByName(string typeName)
        {
            if (_typeNameTable == null)
            {
                InitTypeTables();
            }
            for (int i=0; i<_typeNameTable.Length; i++)
            {
                if(0 == string.CompareOrdinal(_typeNameTable[i], typeName))
                {
                    return i;
                }
            }
            return -1;
        }

        private int LookupTypeIndexByType(global::System.Type type)
        {
            if (_typeTable == null)
            {
                InitTypeTables();
            }
            for(int i=0; i<_typeTable.Length; i++)
            {
                if(type == _typeTable[i])
                {
                    return i;
                }
            }
            return -1;
        }

        private object Activate_5_ShellView() { return new global::Microsoft.Maui.Controls.Platform.ShellView(); }
        private object Activate_6_RootNavigationView() { return new global::Microsoft.Maui.Platform.RootNavigationView(); }
        private object Activate_7_MauiNavigationView() { return new global::Microsoft.Maui.Platform.MauiNavigationView(); }
        private object Activate_8_NavigationView() { return new global::Microsoft.UI.Xaml.Controls.NavigationView(); }
        private object Activate_30_NavigationViewTemplateSettings() { return new global::Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings(); }
        private object Activate_32_TreeViewNode() { return new global::Microsoft.UI.Xaml.Controls.TreeViewNode(); }
        private void StaticInitializer_0_MauiWinUIApplication() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.Maui.MauiWinUIApplication).TypeHandle);
        private void StaticInitializer_3_IServiceProvider() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.IServiceProvider).TypeHandle);
        private void StaticInitializer_4_IApplication() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.Maui.IApplication).TypeHandle);
        private void StaticInitializer_5_ShellView() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.Maui.Controls.Platform.ShellView).TypeHandle);
        private void StaticInitializer_6_RootNavigationView() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.Maui.Platform.RootNavigationView).TypeHandle);
        private void StaticInitializer_7_MauiNavigationView() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.Maui.Platform.MauiNavigationView).TypeHandle);
        private void StaticInitializer_8_NavigationView() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.NavigationView).TypeHandle);
        private void StaticInitializer_10_Thickness() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Thickness).TypeHandle);
        private void StaticInitializer_11_ValueType() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.ValueType).TypeHandle);
        private void StaticInitializer_17_NavigationViewDisplayMode() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.NavigationViewDisplayMode).TypeHandle);
        private void StaticInitializer_18_Enum() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.Enum).TypeHandle);
        private void StaticInitializer_19_IList() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.Collections.Generic.IList<global::System.Object>).TypeHandle);
        private void StaticInitializer_21_NavigationViewBackButtonVisible() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.NavigationViewBackButtonVisible).TypeHandle);
        private void StaticInitializer_25_NavigationViewOverflowLabelMode() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.NavigationViewOverflowLabelMode).TypeHandle);
        private void StaticInitializer_26_NavigationViewPaneDisplayMode() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.NavigationViewPaneDisplayMode).TypeHandle);
        private void StaticInitializer_28_NavigationViewSelectionFollowsFocus() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.NavigationViewSelectionFollowsFocus).TypeHandle);
        private void StaticInitializer_29_NavigationViewShoulderNavigationEnabled() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.NavigationViewShoulderNavigationEnabled).TypeHandle);
        private void StaticInitializer_30_NavigationViewTemplateSettings() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings).TypeHandle);
        private void StaticInitializer_32_TreeViewNode() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.TreeViewNode).TypeHandle);
        private void StaticInitializer_33_IList() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.Collections.Generic.IList<global::Microsoft.UI.Xaml.Controls.TreeViewNode>).TypeHandle);
        private void VectorAdd_19_IList(object instance, object item)
        {
            var collection = (global::System.Collections.Generic.ICollection<global::System.Object>)instance;
            var newItem = (global::System.Object)item;
            collection.Add(newItem);
        }
        private void VectorAdd_33_IList(object instance, object item)
        {
            var collection = (global::System.Collections.Generic.ICollection<global::Microsoft.UI.Xaml.Controls.TreeViewNode>)instance;
            var newItem = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)item;
            collection.Add(newItem);
        }

        private global::Microsoft.UI.Xaml.Markup.IXamlType CreateXamlType(int typeIndex)
        {
            global::unitedprint.unitedprint_XamlTypeInfo.XamlSystemBaseType xamlType = null;
            global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType userType;
            string typeName = _typeNameTable[typeIndex];
            global::System.Type type = _typeTable[typeIndex];

            switch (typeIndex)
            {

            case 0:   //  Microsoft.Maui.MauiWinUIApplication
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Application"));
                userType.StaticInitializer = StaticInitializer_0_MauiWinUIApplication;
                userType.AddMemberName("LaunchActivatedEventArgs");
                userType.AddMemberName("Services");
                userType.AddMemberName("Application");
                xamlType = userType;
                break;

            case 1:   //  Microsoft.UI.Xaml.Application
                xamlType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 2:   //  Microsoft.UI.Xaml.LaunchActivatedEventArgs
                xamlType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 3:   //  System.IServiceProvider
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, null);
                userType.StaticInitializer = StaticInitializer_3_IServiceProvider;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 4:   //  Microsoft.Maui.IApplication
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, null);
                userType.StaticInitializer = StaticInitializer_4_IApplication;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 5:   //  Microsoft.Maui.Controls.Platform.ShellView
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.Maui.Platform.RootNavigationView"));
                userType.Activator = Activate_5_ShellView;
                userType.StaticInitializer = StaticInitializer_5_ShellView;
                userType.SetIsBindable();
                xamlType = userType;
                break;

            case 6:   //  Microsoft.Maui.Platform.RootNavigationView
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.Maui.Platform.MauiNavigationView"));
                userType.Activator = Activate_6_RootNavigationView;
                userType.StaticInitializer = StaticInitializer_6_RootNavigationView;
                userType.SetIsBindable();
                xamlType = userType;
                break;

            case 7:   //  Microsoft.Maui.Platform.MauiNavigationView
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView"));
                userType.Activator = Activate_7_MauiNavigationView;
                userType.StaticInitializer = StaticInitializer_7_MauiNavigationView;
                userType.AddMemberName("NavigationViewBackButtonMargin");
                userType.SetIsBindable();
                xamlType = userType;
                break;

            case 8:   //  Microsoft.UI.Xaml.Controls.NavigationView
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ContentControl"));
                userType.Activator = Activate_8_NavigationView;
                userType.StaticInitializer = StaticInitializer_8_NavigationView;
                userType.AddMemberName("AlwaysShowHeader");
                userType.AddMemberName("AutoSuggestBox");
                userType.AddMemberName("CompactModeThresholdWidth");
                userType.AddMemberName("CompactPaneLength");
                userType.AddMemberName("ContentOverlay");
                userType.AddMemberName("DisplayMode");
                userType.AddMemberName("ExpandedModeThresholdWidth");
                userType.AddMemberName("FooterMenuItems");
                userType.AddMemberName("FooterMenuItemsSource");
                userType.AddMemberName("Header");
                userType.AddMemberName("HeaderTemplate");
                userType.AddMemberName("IsBackButtonVisible");
                userType.AddMemberName("IsBackEnabled");
                userType.AddMemberName("IsPaneOpen");
                userType.AddMemberName("IsPaneToggleButtonVisible");
                userType.AddMemberName("IsPaneVisible");
                userType.AddMemberName("IsSettingsVisible");
                userType.AddMemberName("IsTitleBarAutoPaddingEnabled");
                userType.AddMemberName("MenuItemContainerStyle");
                userType.AddMemberName("MenuItemContainerStyleSelector");
                userType.AddMemberName("MenuItemTemplate");
                userType.AddMemberName("MenuItemTemplateSelector");
                userType.AddMemberName("MenuItems");
                userType.AddMemberName("MenuItemsSource");
                userType.AddMemberName("OpenPaneLength");
                userType.AddMemberName("OverflowLabelMode");
                userType.AddMemberName("PaneCustomContent");
                userType.AddMemberName("PaneDisplayMode");
                userType.AddMemberName("PaneFooter");
                userType.AddMemberName("PaneHeader");
                userType.AddMemberName("PaneTitle");
                userType.AddMemberName("PaneToggleButtonStyle");
                userType.AddMemberName("SelectedItem");
                userType.AddMemberName("SelectionFollowsFocus");
                userType.AddMemberName("SettingsItem");
                userType.AddMemberName("ShoulderNavigationEnabled");
                userType.AddMemberName("TemplateSettings");
                xamlType = userType;
                break;

            case 9:   //  Microsoft.UI.Xaml.Controls.ContentControl
                xamlType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 10:   //  Microsoft.UI.Xaml.Thickness
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.ValueType"));
                userType.StaticInitializer = StaticInitializer_10_Thickness;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 11:   //  System.ValueType
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.StaticInitializer = StaticInitializer_11_ValueType;
                xamlType = userType;
                break;

            case 12:   //  Object
                xamlType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 13:   //  Boolean
                xamlType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 14:   //  Microsoft.UI.Xaml.Controls.AutoSuggestBox
                xamlType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 15:   //  Double
                xamlType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 16:   //  Microsoft.UI.Xaml.UIElement
                xamlType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 17:   //  Microsoft.UI.Xaml.Controls.NavigationViewDisplayMode
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Enum"));
                userType.StaticInitializer = StaticInitializer_17_NavigationViewDisplayMode;
                userType.AddEnumValue("Minimal", global::Microsoft.UI.Xaml.Controls.NavigationViewDisplayMode.Minimal);
                userType.AddEnumValue("Compact", global::Microsoft.UI.Xaml.Controls.NavigationViewDisplayMode.Compact);
                userType.AddEnumValue("Expanded", global::Microsoft.UI.Xaml.Controls.NavigationViewDisplayMode.Expanded);
                xamlType = userType;
                break;

            case 18:   //  System.Enum
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.ValueType"));
                userType.StaticInitializer = StaticInitializer_18_Enum;
                xamlType = userType;
                break;

            case 19:   //  System.Collections.Generic.IList`1<Object>
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, null);
                userType.StaticInitializer = StaticInitializer_19_IList;
                userType.CollectionAdd = VectorAdd_19_IList;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 20:   //  Microsoft.UI.Xaml.DataTemplate
                xamlType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 21:   //  Microsoft.UI.Xaml.Controls.NavigationViewBackButtonVisible
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Enum"));
                userType.StaticInitializer = StaticInitializer_21_NavigationViewBackButtonVisible;
                userType.AddEnumValue("Collapsed", global::Microsoft.UI.Xaml.Controls.NavigationViewBackButtonVisible.Collapsed);
                userType.AddEnumValue("Visible", global::Microsoft.UI.Xaml.Controls.NavigationViewBackButtonVisible.Visible);
                userType.AddEnumValue("Auto", global::Microsoft.UI.Xaml.Controls.NavigationViewBackButtonVisible.Auto);
                xamlType = userType;
                break;

            case 22:   //  Microsoft.UI.Xaml.Style
                xamlType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 23:   //  Microsoft.UI.Xaml.Controls.StyleSelector
                xamlType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 24:   //  Microsoft.UI.Xaml.Controls.DataTemplateSelector
                xamlType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 25:   //  Microsoft.UI.Xaml.Controls.NavigationViewOverflowLabelMode
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Enum"));
                userType.StaticInitializer = StaticInitializer_25_NavigationViewOverflowLabelMode;
                userType.AddEnumValue("MoreLabel", global::Microsoft.UI.Xaml.Controls.NavigationViewOverflowLabelMode.MoreLabel);
                userType.AddEnumValue("NoLabel", global::Microsoft.UI.Xaml.Controls.NavigationViewOverflowLabelMode.NoLabel);
                xamlType = userType;
                break;

            case 26:   //  Microsoft.UI.Xaml.Controls.NavigationViewPaneDisplayMode
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Enum"));
                userType.StaticInitializer = StaticInitializer_26_NavigationViewPaneDisplayMode;
                userType.AddEnumValue("Auto", global::Microsoft.UI.Xaml.Controls.NavigationViewPaneDisplayMode.Auto);
                userType.AddEnumValue("Left", global::Microsoft.UI.Xaml.Controls.NavigationViewPaneDisplayMode.Left);
                userType.AddEnumValue("Top", global::Microsoft.UI.Xaml.Controls.NavigationViewPaneDisplayMode.Top);
                userType.AddEnumValue("LeftCompact", global::Microsoft.UI.Xaml.Controls.NavigationViewPaneDisplayMode.LeftCompact);
                userType.AddEnumValue("LeftMinimal", global::Microsoft.UI.Xaml.Controls.NavigationViewPaneDisplayMode.LeftMinimal);
                xamlType = userType;
                break;

            case 27:   //  String
                xamlType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 28:   //  Microsoft.UI.Xaml.Controls.NavigationViewSelectionFollowsFocus
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Enum"));
                userType.StaticInitializer = StaticInitializer_28_NavigationViewSelectionFollowsFocus;
                userType.AddEnumValue("Disabled", global::Microsoft.UI.Xaml.Controls.NavigationViewSelectionFollowsFocus.Disabled);
                userType.AddEnumValue("Enabled", global::Microsoft.UI.Xaml.Controls.NavigationViewSelectionFollowsFocus.Enabled);
                xamlType = userType;
                break;

            case 29:   //  Microsoft.UI.Xaml.Controls.NavigationViewShoulderNavigationEnabled
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Enum"));
                userType.StaticInitializer = StaticInitializer_29_NavigationViewShoulderNavigationEnabled;
                userType.AddEnumValue("WhenSelectionFollowsFocus", global::Microsoft.UI.Xaml.Controls.NavigationViewShoulderNavigationEnabled.WhenSelectionFollowsFocus);
                userType.AddEnumValue("Always", global::Microsoft.UI.Xaml.Controls.NavigationViewShoulderNavigationEnabled.Always);
                userType.AddEnumValue("Never", global::Microsoft.UI.Xaml.Controls.NavigationViewShoulderNavigationEnabled.Never);
                xamlType = userType;
                break;

            case 30:   //  Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.DependencyObject"));
                userType.StaticInitializer = StaticInitializer_30_NavigationViewTemplateSettings;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 31:   //  Microsoft.UI.Xaml.DependencyObject
                xamlType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 32:   //  Microsoft.UI.Xaml.Controls.TreeViewNode
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.DependencyObject"));
                userType.Activator = Activate_32_TreeViewNode;
                userType.StaticInitializer = StaticInitializer_32_TreeViewNode;
                userType.AddMemberName("Children");
                userType.AddMemberName("Content");
                userType.AddMemberName("Depth");
                userType.AddMemberName("HasChildren");
                userType.AddMemberName("HasUnrealizedChildren");
                userType.AddMemberName("IsExpanded");
                userType.AddMemberName("Parent");
                userType.SetIsBindable();
                xamlType = userType;
                break;

            case 33:   //  System.Collections.Generic.IList`1<Microsoft.UI.Xaml.Controls.TreeViewNode>
                userType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType(this, typeName, type, null);
                userType.StaticInitializer = StaticInitializer_33_IList;
                userType.CollectionAdd = VectorAdd_33_IList;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 34:   //  Int32
                xamlType = new global::unitedprint.unitedprint_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;
            }
            return xamlType;
        }

        private global::System.Collections.Generic.List<global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider> _otherProviders;
        private global::System.Collections.Generic.List<global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider> OtherProviders
        {
            get
            {
                if(_otherProviders == null)
                {
                    var otherProviders = new global::System.Collections.Generic.List<global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider>();
                    global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider provider;
                    provider = new global::Microsoft.UI.Xaml.XamlTypeInfo.XamlControlsXamlMetaDataProvider() as global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider;
                    otherProviders.Add(provider); 
                    provider = new global::Microsoft.Maui.Controls.Compatibility.Compatibility_XamlTypeInfo.XamlMetaDataProvider() as global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider;
                    otherProviders.Add(provider); 
                    provider = new global::Microsoft.Maui.Controls.Controls_Core_XamlTypeInfo.XamlMetaDataProvider() as global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider;
                    otherProviders.Add(provider); 
                    provider = new global::Microsoft.Maui.Core_XamlTypeInfo.XamlMetaDataProvider() as global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider;
                    otherProviders.Add(provider); 
                    _otherProviders = otherProviders;
                }
                return _otherProviders;
            }
        }

        private global::Microsoft.UI.Xaml.Markup.IXamlType CheckOtherMetadataProvidersForName(string typeName)
        {
            global::Microsoft.UI.Xaml.Markup.IXamlType xamlType = null;
            global::Microsoft.UI.Xaml.Markup.IXamlType foundXamlType = null;
            foreach(global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider xmp in OtherProviders)
            {
                xamlType = xmp.GetXamlType(typeName);
                if(xamlType != null)
                {
                    if(xamlType.IsConstructible)    // not Constructible means it might be a Return Type Stub
                    {
                        return xamlType;
                    }
                    foundXamlType = xamlType;
                }
            }
            return foundXamlType;
        }

        private global::Microsoft.UI.Xaml.Markup.IXamlType CheckOtherMetadataProvidersForType(global::System.Type type)
        {
            global::Microsoft.UI.Xaml.Markup.IXamlType xamlType = null;
            global::Microsoft.UI.Xaml.Markup.IXamlType foundXamlType = null;
            foreach(global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider xmp in OtherProviders)
            {
                xamlType = xmp.GetXamlType(type);
                if(xamlType != null)
                {
                    if(xamlType.IsConstructible)    // not Constructible means it might be a Return Type Stub
                    {
                        return xamlType;
                    }
                    foundXamlType = xamlType;
                }
            }
            return foundXamlType;
        }

        private object get_0_MauiWinUIApplication_LaunchActivatedEventArgs(object instance)
        {
            var that = (global::Microsoft.Maui.MauiWinUIApplication)instance;
            return that.LaunchActivatedEventArgs;
        }
        private object get_1_MauiWinUIApplication_Services(object instance)
        {
            var that = (global::Microsoft.Maui.MauiWinUIApplication)instance;
            return that.Services;
        }
        private object get_2_MauiWinUIApplication_Application(object instance)
        {
            var that = (global::Microsoft.Maui.MauiWinUIApplication)instance;
            return that.Application;
        }
        private object get_3_MauiNavigationView_NavigationViewBackButtonMargin(object instance)
        {
            var that = (global::Microsoft.Maui.Platform.MauiNavigationView)instance;
            return that.NavigationViewBackButtonMargin;
        }
        private void set_3_MauiNavigationView_NavigationViewBackButtonMargin(object instance, object Value)
        {
            var that = (global::Microsoft.Maui.Platform.MauiNavigationView)instance;
            that.NavigationViewBackButtonMargin = (global::Microsoft.UI.Xaml.Thickness)Value;
        }
        private object get_4_NavigationView_AlwaysShowHeader(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.AlwaysShowHeader;
        }
        private void set_4_NavigationView_AlwaysShowHeader(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.AlwaysShowHeader = (global::System.Boolean)Value;
        }
        private object get_5_NavigationView_AutoSuggestBox(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.AutoSuggestBox;
        }
        private void set_5_NavigationView_AutoSuggestBox(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.AutoSuggestBox = (global::Microsoft.UI.Xaml.Controls.AutoSuggestBox)Value;
        }
        private object get_6_NavigationView_CompactModeThresholdWidth(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.CompactModeThresholdWidth;
        }
        private void set_6_NavigationView_CompactModeThresholdWidth(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.CompactModeThresholdWidth = (global::System.Double)Value;
        }
        private object get_7_NavigationView_CompactPaneLength(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.CompactPaneLength;
        }
        private void set_7_NavigationView_CompactPaneLength(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.CompactPaneLength = (global::System.Double)Value;
        }
        private object get_8_NavigationView_ContentOverlay(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.ContentOverlay;
        }
        private void set_8_NavigationView_ContentOverlay(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.ContentOverlay = (global::Microsoft.UI.Xaml.UIElement)Value;
        }
        private object get_9_NavigationView_DisplayMode(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.DisplayMode;
        }
        private object get_10_NavigationView_ExpandedModeThresholdWidth(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.ExpandedModeThresholdWidth;
        }
        private void set_10_NavigationView_ExpandedModeThresholdWidth(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.ExpandedModeThresholdWidth = (global::System.Double)Value;
        }
        private object get_11_NavigationView_FooterMenuItems(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.FooterMenuItems;
        }
        private object get_12_NavigationView_FooterMenuItemsSource(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.FooterMenuItemsSource;
        }
        private void set_12_NavigationView_FooterMenuItemsSource(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.FooterMenuItemsSource = (global::System.Object)Value;
        }
        private object get_13_NavigationView_Header(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.Header;
        }
        private void set_13_NavigationView_Header(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.Header = (global::System.Object)Value;
        }
        private object get_14_NavigationView_HeaderTemplate(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.HeaderTemplate;
        }
        private void set_14_NavigationView_HeaderTemplate(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.HeaderTemplate = (global::Microsoft.UI.Xaml.DataTemplate)Value;
        }
        private object get_15_NavigationView_IsBackButtonVisible(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.IsBackButtonVisible;
        }
        private void set_15_NavigationView_IsBackButtonVisible(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.IsBackButtonVisible = (global::Microsoft.UI.Xaml.Controls.NavigationViewBackButtonVisible)Value;
        }
        private object get_16_NavigationView_IsBackEnabled(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.IsBackEnabled;
        }
        private void set_16_NavigationView_IsBackEnabled(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.IsBackEnabled = (global::System.Boolean)Value;
        }
        private object get_17_NavigationView_IsPaneOpen(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.IsPaneOpen;
        }
        private void set_17_NavigationView_IsPaneOpen(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.IsPaneOpen = (global::System.Boolean)Value;
        }
        private object get_18_NavigationView_IsPaneToggleButtonVisible(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.IsPaneToggleButtonVisible;
        }
        private void set_18_NavigationView_IsPaneToggleButtonVisible(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.IsPaneToggleButtonVisible = (global::System.Boolean)Value;
        }
        private object get_19_NavigationView_IsPaneVisible(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.IsPaneVisible;
        }
        private void set_19_NavigationView_IsPaneVisible(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.IsPaneVisible = (global::System.Boolean)Value;
        }
        private object get_20_NavigationView_IsSettingsVisible(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.IsSettingsVisible;
        }
        private void set_20_NavigationView_IsSettingsVisible(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.IsSettingsVisible = (global::System.Boolean)Value;
        }
        private object get_21_NavigationView_IsTitleBarAutoPaddingEnabled(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.IsTitleBarAutoPaddingEnabled;
        }
        private void set_21_NavigationView_IsTitleBarAutoPaddingEnabled(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.IsTitleBarAutoPaddingEnabled = (global::System.Boolean)Value;
        }
        private object get_22_NavigationView_MenuItemContainerStyle(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.MenuItemContainerStyle;
        }
        private void set_22_NavigationView_MenuItemContainerStyle(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.MenuItemContainerStyle = (global::Microsoft.UI.Xaml.Style)Value;
        }
        private object get_23_NavigationView_MenuItemContainerStyleSelector(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.MenuItemContainerStyleSelector;
        }
        private void set_23_NavigationView_MenuItemContainerStyleSelector(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.MenuItemContainerStyleSelector = (global::Microsoft.UI.Xaml.Controls.StyleSelector)Value;
        }
        private object get_24_NavigationView_MenuItemTemplate(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.MenuItemTemplate;
        }
        private void set_24_NavigationView_MenuItemTemplate(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.MenuItemTemplate = (global::Microsoft.UI.Xaml.DataTemplate)Value;
        }
        private object get_25_NavigationView_MenuItemTemplateSelector(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.MenuItemTemplateSelector;
        }
        private void set_25_NavigationView_MenuItemTemplateSelector(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.MenuItemTemplateSelector = (global::Microsoft.UI.Xaml.Controls.DataTemplateSelector)Value;
        }
        private object get_26_NavigationView_MenuItems(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.MenuItems;
        }
        private object get_27_NavigationView_MenuItemsSource(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.MenuItemsSource;
        }
        private void set_27_NavigationView_MenuItemsSource(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.MenuItemsSource = (global::System.Object)Value;
        }
        private object get_28_NavigationView_OpenPaneLength(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.OpenPaneLength;
        }
        private void set_28_NavigationView_OpenPaneLength(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.OpenPaneLength = (global::System.Double)Value;
        }
        private object get_29_NavigationView_OverflowLabelMode(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.OverflowLabelMode;
        }
        private void set_29_NavigationView_OverflowLabelMode(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.OverflowLabelMode = (global::Microsoft.UI.Xaml.Controls.NavigationViewOverflowLabelMode)Value;
        }
        private object get_30_NavigationView_PaneCustomContent(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.PaneCustomContent;
        }
        private void set_30_NavigationView_PaneCustomContent(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.PaneCustomContent = (global::Microsoft.UI.Xaml.UIElement)Value;
        }
        private object get_31_NavigationView_PaneDisplayMode(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.PaneDisplayMode;
        }
        private void set_31_NavigationView_PaneDisplayMode(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.PaneDisplayMode = (global::Microsoft.UI.Xaml.Controls.NavigationViewPaneDisplayMode)Value;
        }
        private object get_32_NavigationView_PaneFooter(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.PaneFooter;
        }
        private void set_32_NavigationView_PaneFooter(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.PaneFooter = (global::Microsoft.UI.Xaml.UIElement)Value;
        }
        private object get_33_NavigationView_PaneHeader(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.PaneHeader;
        }
        private void set_33_NavigationView_PaneHeader(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.PaneHeader = (global::Microsoft.UI.Xaml.UIElement)Value;
        }
        private object get_34_NavigationView_PaneTitle(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.PaneTitle;
        }
        private void set_34_NavigationView_PaneTitle(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.PaneTitle = (global::System.String)Value;
        }
        private object get_35_NavigationView_PaneToggleButtonStyle(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.PaneToggleButtonStyle;
        }
        private void set_35_NavigationView_PaneToggleButtonStyle(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.PaneToggleButtonStyle = (global::Microsoft.UI.Xaml.Style)Value;
        }
        private object get_36_NavigationView_SelectedItem(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.SelectedItem;
        }
        private void set_36_NavigationView_SelectedItem(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.SelectedItem = (global::System.Object)Value;
        }
        private object get_37_NavigationView_SelectionFollowsFocus(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.SelectionFollowsFocus;
        }
        private void set_37_NavigationView_SelectionFollowsFocus(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.SelectionFollowsFocus = (global::Microsoft.UI.Xaml.Controls.NavigationViewSelectionFollowsFocus)Value;
        }
        private object get_38_NavigationView_SettingsItem(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.SettingsItem;
        }
        private object get_39_NavigationView_ShoulderNavigationEnabled(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.ShoulderNavigationEnabled;
        }
        private void set_39_NavigationView_ShoulderNavigationEnabled(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            that.ShoulderNavigationEnabled = (global::Microsoft.UI.Xaml.Controls.NavigationViewShoulderNavigationEnabled)Value;
        }
        private object get_40_NavigationView_TemplateSettings(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NavigationView)instance;
            return that.TemplateSettings;
        }
        private object get_41_TreeViewNode_Children(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.Children;
        }
        private object get_42_TreeViewNode_Content(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.Content;
        }
        private void set_42_TreeViewNode_Content(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            that.Content = (global::System.Object)Value;
        }
        private object get_43_TreeViewNode_Depth(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.Depth;
        }
        private object get_44_TreeViewNode_HasChildren(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.HasChildren;
        }
        private object get_45_TreeViewNode_HasUnrealizedChildren(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.HasUnrealizedChildren;
        }
        private void set_45_TreeViewNode_HasUnrealizedChildren(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            that.HasUnrealizedChildren = (global::System.Boolean)Value;
        }
        private object get_46_TreeViewNode_IsExpanded(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.IsExpanded;
        }
        private void set_46_TreeViewNode_IsExpanded(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            that.IsExpanded = (global::System.Boolean)Value;
        }
        private object get_47_TreeViewNode_Parent(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.Parent;
        }

        private global::Microsoft.UI.Xaml.Markup.IXamlMember CreateXamlMember(string longMemberName)
        {
            global::unitedprint.unitedprint_XamlTypeInfo.XamlMember xamlMember = null;
            global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType userType;

            switch (longMemberName)
            {
            case "Microsoft.Maui.MauiWinUIApplication.LaunchActivatedEventArgs":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.Maui.MauiWinUIApplication");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "LaunchActivatedEventArgs", "Microsoft.UI.Xaml.LaunchActivatedEventArgs");
                xamlMember.Getter = get_0_MauiWinUIApplication_LaunchActivatedEventArgs;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.Maui.MauiWinUIApplication.Services":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.Maui.MauiWinUIApplication");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "Services", "System.IServiceProvider");
                xamlMember.Getter = get_1_MauiWinUIApplication_Services;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.Maui.MauiWinUIApplication.Application":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.Maui.MauiWinUIApplication");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "Application", "Microsoft.Maui.IApplication");
                xamlMember.Getter = get_2_MauiWinUIApplication_Application;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.Maui.Platform.MauiNavigationView.NavigationViewBackButtonMargin":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.Maui.Platform.MauiNavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "NavigationViewBackButtonMargin", "Microsoft.UI.Xaml.Thickness");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_3_MauiNavigationView_NavigationViewBackButtonMargin;
                xamlMember.Setter = set_3_MauiNavigationView_NavigationViewBackButtonMargin;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.AlwaysShowHeader":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "AlwaysShowHeader", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_4_NavigationView_AlwaysShowHeader;
                xamlMember.Setter = set_4_NavigationView_AlwaysShowHeader;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.AutoSuggestBox":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "AutoSuggestBox", "Microsoft.UI.Xaml.Controls.AutoSuggestBox");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_5_NavigationView_AutoSuggestBox;
                xamlMember.Setter = set_5_NavigationView_AutoSuggestBox;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.CompactModeThresholdWidth":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "CompactModeThresholdWidth", "Double");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_6_NavigationView_CompactModeThresholdWidth;
                xamlMember.Setter = set_6_NavigationView_CompactModeThresholdWidth;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.CompactPaneLength":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "CompactPaneLength", "Double");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_7_NavigationView_CompactPaneLength;
                xamlMember.Setter = set_7_NavigationView_CompactPaneLength;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.ContentOverlay":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "ContentOverlay", "Microsoft.UI.Xaml.UIElement");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_8_NavigationView_ContentOverlay;
                xamlMember.Setter = set_8_NavigationView_ContentOverlay;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.DisplayMode":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "DisplayMode", "Microsoft.UI.Xaml.Controls.NavigationViewDisplayMode");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_9_NavigationView_DisplayMode;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.ExpandedModeThresholdWidth":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "ExpandedModeThresholdWidth", "Double");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_10_NavigationView_ExpandedModeThresholdWidth;
                xamlMember.Setter = set_10_NavigationView_ExpandedModeThresholdWidth;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.FooterMenuItems":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "FooterMenuItems", "System.Collections.Generic.IList`1<Object>");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_11_NavigationView_FooterMenuItems;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.FooterMenuItemsSource":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "FooterMenuItemsSource", "Object");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_12_NavigationView_FooterMenuItemsSource;
                xamlMember.Setter = set_12_NavigationView_FooterMenuItemsSource;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.Header":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "Header", "Object");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_13_NavigationView_Header;
                xamlMember.Setter = set_13_NavigationView_Header;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.HeaderTemplate":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "HeaderTemplate", "Microsoft.UI.Xaml.DataTemplate");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_14_NavigationView_HeaderTemplate;
                xamlMember.Setter = set_14_NavigationView_HeaderTemplate;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.IsBackButtonVisible":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "IsBackButtonVisible", "Microsoft.UI.Xaml.Controls.NavigationViewBackButtonVisible");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_15_NavigationView_IsBackButtonVisible;
                xamlMember.Setter = set_15_NavigationView_IsBackButtonVisible;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.IsBackEnabled":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "IsBackEnabled", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_16_NavigationView_IsBackEnabled;
                xamlMember.Setter = set_16_NavigationView_IsBackEnabled;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.IsPaneOpen":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "IsPaneOpen", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_17_NavigationView_IsPaneOpen;
                xamlMember.Setter = set_17_NavigationView_IsPaneOpen;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.IsPaneToggleButtonVisible":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "IsPaneToggleButtonVisible", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_18_NavigationView_IsPaneToggleButtonVisible;
                xamlMember.Setter = set_18_NavigationView_IsPaneToggleButtonVisible;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.IsPaneVisible":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "IsPaneVisible", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_19_NavigationView_IsPaneVisible;
                xamlMember.Setter = set_19_NavigationView_IsPaneVisible;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.IsSettingsVisible":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "IsSettingsVisible", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_20_NavigationView_IsSettingsVisible;
                xamlMember.Setter = set_20_NavigationView_IsSettingsVisible;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.IsTitleBarAutoPaddingEnabled":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "IsTitleBarAutoPaddingEnabled", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_21_NavigationView_IsTitleBarAutoPaddingEnabled;
                xamlMember.Setter = set_21_NavigationView_IsTitleBarAutoPaddingEnabled;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.MenuItemContainerStyle":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "MenuItemContainerStyle", "Microsoft.UI.Xaml.Style");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_22_NavigationView_MenuItemContainerStyle;
                xamlMember.Setter = set_22_NavigationView_MenuItemContainerStyle;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.MenuItemContainerStyleSelector":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "MenuItemContainerStyleSelector", "Microsoft.UI.Xaml.Controls.StyleSelector");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_23_NavigationView_MenuItemContainerStyleSelector;
                xamlMember.Setter = set_23_NavigationView_MenuItemContainerStyleSelector;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.MenuItemTemplate":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "MenuItemTemplate", "Microsoft.UI.Xaml.DataTemplate");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_24_NavigationView_MenuItemTemplate;
                xamlMember.Setter = set_24_NavigationView_MenuItemTemplate;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.MenuItemTemplateSelector":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "MenuItemTemplateSelector", "Microsoft.UI.Xaml.Controls.DataTemplateSelector");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_25_NavigationView_MenuItemTemplateSelector;
                xamlMember.Setter = set_25_NavigationView_MenuItemTemplateSelector;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.MenuItems":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "MenuItems", "System.Collections.Generic.IList`1<Object>");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_26_NavigationView_MenuItems;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.MenuItemsSource":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "MenuItemsSource", "Object");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_27_NavigationView_MenuItemsSource;
                xamlMember.Setter = set_27_NavigationView_MenuItemsSource;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.OpenPaneLength":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "OpenPaneLength", "Double");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_28_NavigationView_OpenPaneLength;
                xamlMember.Setter = set_28_NavigationView_OpenPaneLength;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.OverflowLabelMode":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "OverflowLabelMode", "Microsoft.UI.Xaml.Controls.NavigationViewOverflowLabelMode");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_29_NavigationView_OverflowLabelMode;
                xamlMember.Setter = set_29_NavigationView_OverflowLabelMode;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.PaneCustomContent":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "PaneCustomContent", "Microsoft.UI.Xaml.UIElement");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_30_NavigationView_PaneCustomContent;
                xamlMember.Setter = set_30_NavigationView_PaneCustomContent;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.PaneDisplayMode":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "PaneDisplayMode", "Microsoft.UI.Xaml.Controls.NavigationViewPaneDisplayMode");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_31_NavigationView_PaneDisplayMode;
                xamlMember.Setter = set_31_NavigationView_PaneDisplayMode;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.PaneFooter":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "PaneFooter", "Microsoft.UI.Xaml.UIElement");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_32_NavigationView_PaneFooter;
                xamlMember.Setter = set_32_NavigationView_PaneFooter;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.PaneHeader":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "PaneHeader", "Microsoft.UI.Xaml.UIElement");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_33_NavigationView_PaneHeader;
                xamlMember.Setter = set_33_NavigationView_PaneHeader;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.PaneTitle":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "PaneTitle", "String");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_34_NavigationView_PaneTitle;
                xamlMember.Setter = set_34_NavigationView_PaneTitle;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.PaneToggleButtonStyle":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "PaneToggleButtonStyle", "Microsoft.UI.Xaml.Style");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_35_NavigationView_PaneToggleButtonStyle;
                xamlMember.Setter = set_35_NavigationView_PaneToggleButtonStyle;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.SelectedItem":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "SelectedItem", "Object");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_36_NavigationView_SelectedItem;
                xamlMember.Setter = set_36_NavigationView_SelectedItem;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.SelectionFollowsFocus":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "SelectionFollowsFocus", "Microsoft.UI.Xaml.Controls.NavigationViewSelectionFollowsFocus");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_37_NavigationView_SelectionFollowsFocus;
                xamlMember.Setter = set_37_NavigationView_SelectionFollowsFocus;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.SettingsItem":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "SettingsItem", "Object");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_38_NavigationView_SettingsItem;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.ShoulderNavigationEnabled":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "ShoulderNavigationEnabled", "Microsoft.UI.Xaml.Controls.NavigationViewShoulderNavigationEnabled");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_39_NavigationView_ShoulderNavigationEnabled;
                xamlMember.Setter = set_39_NavigationView_ShoulderNavigationEnabled;
                break;
            case "Microsoft.UI.Xaml.Controls.NavigationView.TemplateSettings":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NavigationView");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "TemplateSettings", "Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_40_NavigationView_TemplateSettings;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.Children":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "Children", "System.Collections.Generic.IList`1<Microsoft.UI.Xaml.Controls.TreeViewNode>");
                xamlMember.Getter = get_41_TreeViewNode_Children;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.Content":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "Content", "Object");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_42_TreeViewNode_Content;
                xamlMember.Setter = set_42_TreeViewNode_Content;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.Depth":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "Depth", "Int32");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_43_TreeViewNode_Depth;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.HasChildren":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "HasChildren", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_44_TreeViewNode_HasChildren;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.HasUnrealizedChildren":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "HasUnrealizedChildren", "Boolean");
                xamlMember.Getter = get_45_TreeViewNode_HasUnrealizedChildren;
                xamlMember.Setter = set_45_TreeViewNode_HasUnrealizedChildren;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.IsExpanded":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "IsExpanded", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_46_TreeViewNode_IsExpanded;
                xamlMember.Setter = set_46_TreeViewNode_IsExpanded;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.Parent":
                userType = (global::unitedprint.unitedprint_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::unitedprint.unitedprint_XamlTypeInfo.XamlMember(this, "Parent", "Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember.Getter = get_47_TreeViewNode_Parent;
                xamlMember.SetIsReadOnly();
                break;
            }
            return xamlMember;
        }
    }

    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2409")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    internal partial class XamlSystemBaseType : global::Microsoft.UI.Xaml.Markup.IXamlType
    {
        string _fullName;
        global::System.Type _underlyingType;

        public XamlSystemBaseType(string fullName, 
            global::System.Type underlyingType)
        {
            _fullName = fullName;
            _underlyingType = underlyingType;
        }

        public string FullName { get { return _fullName; } }

        public global::System.Type UnderlyingType
        {
            get
            {
                return _underlyingType;
            }
        }

        virtual public global::Microsoft.UI.Xaml.Markup.IXamlType BaseType { get { throw new global::System.NotImplementedException(); } }
        virtual public global::Microsoft.UI.Xaml.Markup.IXamlMember ContentProperty { get { throw new global::System.NotImplementedException(); } }
        virtual public global::Microsoft.UI.Xaml.Markup.IXamlMember GetMember(string name) { throw new global::System.NotImplementedException(); }
        virtual public bool IsArray { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsCollection { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsConstructible { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsDictionary { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsMarkupExtension { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsBindable { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsReturnTypeStub { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsLocalType { get { throw new global::System.NotImplementedException(); } }
        virtual public global::Microsoft.UI.Xaml.Markup.IXamlType ItemType { get { throw new global::System.NotImplementedException(); } }
        virtual public global::Microsoft.UI.Xaml.Markup.IXamlType KeyType { get { throw new global::System.NotImplementedException(); } }
        virtual public global::Microsoft.UI.Xaml.Markup.IXamlType BoxedType { get { throw new global::System.NotImplementedException(); } }
        virtual public object ActivateInstance() { throw new global::System.NotImplementedException(); }
        virtual public void AddToMap(object instance, object key, object item)  { throw new global::System.NotImplementedException(); }
        virtual public void AddToVector(object instance, object item)  { throw new global::System.NotImplementedException(); }
        virtual public void RunInitializer()   { throw new global::System.NotImplementedException(); }
        virtual public object CreateFromString(string input)   { throw new global::System.NotImplementedException(); }
    }
    
    internal delegate object Activator();
    internal delegate void StaticInitializer();
    internal delegate void AddToCollection(object instance, object item);
    internal delegate void AddToDictionary(object instance, object key, object item);
    internal delegate object CreateFromStringMethod(string args);
    internal delegate object BoxInstanceMethod(object instance);

    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2409")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    internal partial class XamlUserType : global::unitedprint.unitedprint_XamlTypeInfo.XamlSystemBaseType
        , global::Microsoft.UI.Xaml.Markup.IXamlType
    {
        global::unitedprint.unitedprint_XamlTypeInfo.XamlTypeInfoProvider _provider;
        global::Microsoft.UI.Xaml.Markup.IXamlType _baseType;
        global::Microsoft.UI.Xaml.Markup.IXamlType _boxedType;
        bool _isArray;
        bool _isMarkupExtension;
        bool _isBindable;
        bool _isReturnTypeStub;
        bool _isLocalType;

        string _contentPropertyName;
        string _itemTypeName;
        string _keyTypeName;
        global::System.Collections.Generic.Dictionary<string, string> _memberNames;
        global::System.Collections.Generic.Dictionary<string, object> _enumValues;

        public XamlUserType(global::unitedprint.unitedprint_XamlTypeInfo.XamlTypeInfoProvider provider, string fullName, 
            global::System.Type fullType, global::Microsoft.UI.Xaml.Markup.IXamlType baseType)
            :base(fullName, fullType)
        {
            _provider = provider;
            _baseType = baseType;
        }

        // --- Interface methods ----

        override public global::Microsoft.UI.Xaml.Markup.IXamlType BaseType { get { return _baseType; } }
        override public bool IsArray { get { return _isArray; } }
        override public bool IsCollection { get { return (CollectionAdd != null); } }
        override public bool IsConstructible { get { return (Activator != null); } }
        override public bool IsDictionary { get { return (DictionaryAdd != null); } }
        override public bool IsMarkupExtension { get { return _isMarkupExtension; } }
        override public bool IsBindable { get { return _isBindable; } }
        override public bool IsReturnTypeStub { get { return _isReturnTypeStub; } }
        override public bool IsLocalType { get { return _isLocalType; } }
        override public global::Microsoft.UI.Xaml.Markup.IXamlType BoxedType { get { return _boxedType; } }

        override public global::Microsoft.UI.Xaml.Markup.IXamlMember ContentProperty
        {
            get { return _provider.GetMemberByLongName(_contentPropertyName); }
        }

        override public global::Microsoft.UI.Xaml.Markup.IXamlType ItemType
        {
            get { return _provider.GetXamlTypeByName(_itemTypeName); }
        }

        override public global::Microsoft.UI.Xaml.Markup.IXamlType KeyType
        {
            get { return _provider.GetXamlTypeByName(_keyTypeName); }
        }

        override public global::Microsoft.UI.Xaml.Markup.IXamlMember GetMember(string name)
        {
            if (_memberNames == null)
            {
                return null;
            }
            string longName;
            if (_memberNames.TryGetValue(name, out longName))
            {
                return _provider.GetMemberByLongName(longName);
            }
            return null;
        }

        override public object ActivateInstance()
        {
            return Activator(); 
        }

        override public void AddToMap(object instance, object key, object item) 
        {
            DictionaryAdd(instance, key, item);
        }

        override public void AddToVector(object instance, object item)
        {
            CollectionAdd(instance, item);
        }

        override public void RunInitializer() 
        {
            StaticInitializer();
        }

        override public object CreateFromString(string input)
        {
            if (BoxedType != null)
            {
                return BoxInstance(BoxedType.CreateFromString(input));
            }

            if (CreateFromStringMethod != null)
            {
                return this.CreateFromStringMethod(input);
            }
            else if (_enumValues != null)
            {
                long value = 0;

                string[] valueParts = input.Split(',');

                foreach (string valuePart in valueParts) 
                {
                    object partValue;
                    long enumFieldValue = 0;
                    try
                    {
                        if (_enumValues.TryGetValue(valuePart.Trim(), out partValue))
                        {
                            enumFieldValue = global::System.Convert.ToInt64(partValue);
                        }
                        else
                        {
                            try
                            {
                                enumFieldValue = global::System.Convert.ToInt64(valuePart.Trim());
                            }
                            catch( global::System.FormatException )
                            {
                                foreach( string key in _enumValues.Keys )
                                {
                                    if( string.Compare(valuePart.Trim(), key, global::System.StringComparison.OrdinalIgnoreCase) == 0 )
                                    {
                                        if( _enumValues.TryGetValue(key.Trim(), out partValue) )
                                        {
                                            enumFieldValue = global::System.Convert.ToInt64(partValue);
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        value |= enumFieldValue; 
                    }
                    catch( global::System.FormatException )
                    {
                        throw new global::System.ArgumentException(input, FullName);
                    }
                }

                return global::System.Convert.ChangeType(value, global::System.Enum.GetUnderlyingType(this.UnderlyingType));
            }
            throw new global::System.ArgumentException(input, FullName);
        }

        // --- End of Interface methods

        public Activator Activator { get; set; }
        public StaticInitializer StaticInitializer { get; set; }
        public AddToCollection CollectionAdd { get; set; }
        public AddToDictionary DictionaryAdd { get; set; }
        public CreateFromStringMethod CreateFromStringMethod {get; set; }
        public BoxInstanceMethod BoxInstance {get; set; }

        public void SetContentPropertyName(string contentPropertyName)
        {
            _contentPropertyName = contentPropertyName;
        }

        public void SetIsArray()
        {
            _isArray = true; 
        }

        public void SetIsMarkupExtension()
        {
            _isMarkupExtension = true;
        }

        public void SetIsBindable()
        {
            _isBindable = true;
        }

        public void SetIsReturnTypeStub()
        {
            _isReturnTypeStub = true;
        }

        public void SetIsLocalType()
        {
            _isLocalType = true;
        }

        public void SetItemTypeName(string itemTypeName)
        {
            _itemTypeName = itemTypeName;
        }

        public void SetKeyTypeName(string keyTypeName)
        {
            _keyTypeName = keyTypeName;
        }

        public void SetBoxedType(global::Microsoft.UI.Xaml.Markup.IXamlType boxedType)
        {
            _boxedType = boxedType;
        }

        public object BoxType<T>(object instance) where T: struct
        {
            T unwrapped = (T)instance;
            return new global::System.Nullable<T>(unwrapped);
        }

        public void AddMemberName(string shortName)
        {
            if(_memberNames == null)
            {
                _memberNames =  new global::System.Collections.Generic.Dictionary<string,string>();
            }
            _memberNames.Add(shortName, FullName + "." + shortName);
        }

        public void AddEnumValue(string name, object value)
        {
            if (_enumValues == null)
            {
                _enumValues = new global::System.Collections.Generic.Dictionary<string, object>();
            }
            _enumValues.Add(name, value);
        }
    }

    internal delegate object Getter(object instance);
    internal delegate void Setter(object instance, object value);

    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2409")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    internal partial class XamlMember : global::Microsoft.UI.Xaml.Markup.IXamlMember
    {
        global::unitedprint.unitedprint_XamlTypeInfo.XamlTypeInfoProvider _provider;
        string _name;
        bool _isAttachable;
        bool _isDependencyProperty;
        bool _isReadOnly;

        string _typeName;
        string _targetTypeName;

        public XamlMember(global::unitedprint.unitedprint_XamlTypeInfo.XamlTypeInfoProvider provider, string name, string typeName)
        {
            _name = name;
            _typeName = typeName;
            _provider = provider;
        }

        public string Name { get { return _name; } }

        public global::Microsoft.UI.Xaml.Markup.IXamlType Type
        {
            get { return _provider.GetXamlTypeByName(_typeName); }
        }

        public void SetTargetTypeName(string targetTypeName)
        {
            _targetTypeName = targetTypeName;
        }
        public global::Microsoft.UI.Xaml.Markup.IXamlType TargetType
        {
            get { return _provider.GetXamlTypeByName(_targetTypeName); }
        }

        public void SetIsAttachable() { _isAttachable = true; }
        public bool IsAttachable { get { return _isAttachable; } }

        public void SetIsDependencyProperty() { _isDependencyProperty = true; }
        public bool IsDependencyProperty { get { return _isDependencyProperty; } }

        public void SetIsReadOnly() { _isReadOnly = true; }
        public bool IsReadOnly { get { return _isReadOnly; } }

        public Getter Getter { get; set; }
        public object GetValue(object instance)
        {
            if (Getter != null)
                return Getter(instance);
            else
                throw new global::System.InvalidOperationException("GetValue");
        }

        public Setter Setter { get; set; }
        public void SetValue(object instance, object value)
        {
            if (Setter != null)
                Setter(instance, value);
            else
                throw new global::System.InvalidOperationException("SetValue");
        }
    }
}

