<resources xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:android="http://schemas.android.com/apk/res/android"><!-- 
    The collectionViewScrollBars style will be used as the default style for ItemsViewRenderer (the base renderer
    for CollectionView and CarouselView. We have to use a style to set up the scrollbars because there is currently
    no way to add them via code.
    
    When the renderer is created, we wrap its Context's theme with collectionViewTheme; that way, the 
    collectionViewScrollBars style will be defined. With the style defined (and with the collectionViewStyle 
    attribute defined in attrs.xml), we can apply the collectionViewScrollBars style explicitly to the renderer we are
    creating (and avoid forcing every child control to have scrollbars).
  --><style name="scrollViewScrollBars"><item name="android:scrollbars">vertical|horizontal</item></style><style name="collectionViewTheme"><item name="collectionViewStyle">@style/scrollViewScrollBars</item></style><style name="scrollViewTheme"><item name="scrollViewStyle">@style/scrollViewScrollBars</item></style></resources>