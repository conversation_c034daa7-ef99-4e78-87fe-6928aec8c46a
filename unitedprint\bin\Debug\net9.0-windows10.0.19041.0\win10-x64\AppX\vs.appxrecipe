﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Machine>YHYASOFT</Machine>
    <WindowsUser>yhyasoft</WindowsUser>
    <TargetPlatformIdentifier>Windows</TargetPlatformIdentifier>
    <TargetOsVersion>10.0</TargetOsVersion>
    <TargetOsDescription>Windows 10.0</TargetOsDescription>
    <SolutionConfiguration>Debug|AnyCPU</SolutionConfiguration>
    <PackageArchitecture>x64</PackageArchitecture>
    <PackageIdentityName>com.companyname.unitedprint</PackageIdentityName>
    <PackageIdentityPublisher>CN=User Name</PackageIdentityPublisher>
    <IntermediateOutputPath>C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\</IntermediateOutputPath>
    <RemoteDeploymentType>CopyToDevice</RemoteDeploymentType>
    <PackageRegistrationPath>
    </PackageRegistrationPath>
    <RemoveNonLayoutFiles>false</RemoveNonLayoutFiles>
    <DeployOptionalPackages>false</DeployOptionalPackages>
    <WindowsSdkPath>C:\Program Files %28x86%29\Windows Kits\10\</WindowsSdkPath>
    <LayoutDir>C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\bin\Debug\net9.0-windows10.0.19041.0\win10-x64\AppX</LayoutDir>
    <RegisteredManifestChecksum>4B25D92CE51C6120B412C58934506B5B8C74C102EB1CF0EDC71932F1D460F59D</RegisteredManifestChecksum>
    <RegisteredPackageMoniker>com.companyname.unitedprint_1.0.0.1_x64__9zz4h110yvjzm</RegisteredPackageMoniker>
    <RegisteredUserModeAppID>com.companyname.unitedprint_9zz4h110yvjzm!App</RegisteredUserModeAppID>
    <RegisteredPackageID>com.companyname.unitedprint</RegisteredPackageID>
    <RegisteredPackagePublisher>CN=User Name</RegisteredPackagePublisher>
    <RegisteredVersion>1.0.0.1</RegisteredVersion>
  </PropertyGroup>
  <ItemGroup>
    <AppXManifest Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\bin\Debug\net9.0-windows10.0.19041.0\win10-x64\AppxManifest.xml">
      <PackagePath>AppxManifest.xml</PackagePath>
      <ReRegisterAppIfChanged>true</ReRegisterAppIfChanged>
      <Modified>2025-06-14T13:01:11.190</Modified>
    </AppXManifest>
  </ItemGroup>
  <ItemGroup>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\bin\Debug\net9.0-windows10.0.19041.0\win10-x64\resources.pri">
      <PackagePath>resources.pri</PackagePath>
      <Modified>2025-06-14T12:59:28.039</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\bin\Debug\net9.0-windows10.0.19041.0\win10-x64\unitedprint.runtimeconfig.json">
      <PackagePath>unitedprint.runtimeconfig.json</PackagePath>
      <Modified>2025-06-14T13:00:31.045</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\bin\Debug\net9.0-windows10.0.19041.0\win10-x64\unitedprint.dll">
      <PackagePath>unitedprint.dll</PackagePath>
      <Modified>2025-06-14T13:01:09.917</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\apphost.exe">
      <PackagePath>unitedprint.exe</PackagePath>
      <Modified>2025-06-14T13:01:10.017</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\Resources\Raw\AboutAssets.txt">
      <PackagePath>AboutAssets.txt</PackagePath>
      <Modified>2025-06-14T12:43:28.669</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\sp\splashSplashScreen.scale-100.png">
      <PackagePath>splashSplashScreen.scale-100.png</PackagePath>
      <Modified>2025-06-14T12:54:04.157</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\sp\splashSplashScreen.scale-125.png">
      <PackagePath>splashSplashScreen.scale-125.png</PackagePath>
      <Modified>2025-06-14T12:54:04.179</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\sp\splashSplashScreen.scale-150.png">
      <PackagePath>splashSplashScreen.scale-150.png</PackagePath>
      <Modified>2025-06-14T12:54:04.213</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\sp\splashSplashScreen.scale-200.png">
      <PackagePath>splashSplashScreen.scale-200.png</PackagePath>
      <Modified>2025-06-14T12:54:04.254</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\sp\splashSplashScreen.scale-400.png">
      <PackagePath>splashSplashScreen.scale-400.png</PackagePath>
      <Modified>2025-06-14T12:54:04.455</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\f\OpenSans-Regular.ttf">
      <PackagePath>OpenSans-Regular.ttf</PackagePath>
      <Modified>2025-06-14T12:43:28.637</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\f\OpenSans-Semibold.ttf">
      <PackagePath>OpenSans-Semibold.ttf</PackagePath>
      <Modified>2025-06-14T12:43:28.646</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appicon.ico">
      <PackagePath>appicon.ico</PackagePath>
      <Modified>2025-06-14T12:54:06.185</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-100.png">
      <PackagePath>appiconLargeTile.scale-100.png</PackagePath>
      <Modified>2025-06-14T12:54:06.139</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-125.png">
      <PackagePath>appiconLargeTile.scale-125.png</PackagePath>
      <Modified>2025-06-14T12:54:06.138</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-150.png">
      <PackagePath>appiconLargeTile.scale-150.png</PackagePath>
      <Modified>2025-06-14T12:54:06.137</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-200.png">
      <PackagePath>appiconLargeTile.scale-200.png</PackagePath>
      <Modified>2025-06-14T12:54:06.136</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-400.png">
      <PackagePath>appiconLargeTile.scale-400.png</PackagePath>
      <Modified>2025-06-14T12:54:06.135</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-16.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-16.png</PackagePath>
      <Modified>2025-06-14T12:54:06.167</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-24.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-24.png</PackagePath>
      <Modified>2025-06-14T12:54:06.166</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-256.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-256.png</PackagePath>
      <Modified>2025-06-14T12:54:06.163</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-32.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-32.png</PackagePath>
      <Modified>2025-06-14T12:54:06.165</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-48.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-48.png</PackagePath>
      <Modified>2025-06-14T12:54:06.164</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-16.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-16.png</PackagePath>
      <Modified>2025-06-14T12:54:06.171</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-24.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-24.png</PackagePath>
      <Modified>2025-06-14T12:54:06.171</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-256.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-256.png</PackagePath>
      <Modified>2025-06-14T12:54:06.168</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-32.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-32.png</PackagePath>
      <Modified>2025-06-14T12:54:06.170</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-48.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-48.png</PackagePath>
      <Modified>2025-06-14T12:54:06.169</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-100.png">
      <PackagePath>appiconLogo.scale-100.png</PackagePath>
      <Modified>2025-06-14T12:54:06.184</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-125.png">
      <PackagePath>appiconLogo.scale-125.png</PackagePath>
      <Modified>2025-06-14T12:54:06.183</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-150.png">
      <PackagePath>appiconLogo.scale-150.png</PackagePath>
      <Modified>2025-06-14T12:54:06.182</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-200.png">
      <PackagePath>appiconLogo.scale-200.png</PackagePath>
      <Modified>2025-06-14T12:54:06.181</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-400.png">
      <PackagePath>appiconLogo.scale-400.png</PackagePath>
      <Modified>2025-06-14T12:54:06.180</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-16.png">
      <PackagePath>appiconLogo.targetsize-16.png</PackagePath>
      <Modified>2025-06-14T12:54:06.179</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-24.png">
      <PackagePath>appiconLogo.targetsize-24.png</PackagePath>
      <Modified>2025-06-14T12:54:06.176</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-256.png">
      <PackagePath>appiconLogo.targetsize-256.png</PackagePath>
      <Modified>2025-06-14T12:54:06.172</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-32.png">
      <PackagePath>appiconLogo.targetsize-32.png</PackagePath>
      <Modified>2025-06-14T12:54:06.174</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-48.png">
      <PackagePath>appiconLogo.targetsize-48.png</PackagePath>
      <Modified>2025-06-14T12:54:06.173</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-100.png">
      <PackagePath>appiconMediumTile.scale-100.png</PackagePath>
      <Modified>2025-06-14T12:54:06.150</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-125.png">
      <PackagePath>appiconMediumTile.scale-125.png</PackagePath>
      <Modified>2025-06-14T12:54:06.149</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-150.png">
      <PackagePath>appiconMediumTile.scale-150.png</PackagePath>
      <Modified>2025-06-14T12:54:06.148</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-200.png">
      <PackagePath>appiconMediumTile.scale-200.png</PackagePath>
      <Modified>2025-06-14T12:54:06.147</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-400.png">
      <PackagePath>appiconMediumTile.scale-400.png</PackagePath>
      <Modified>2025-06-14T12:54:06.146</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-100.png">
      <PackagePath>appiconSmallTile.scale-100.png</PackagePath>
      <Modified>2025-06-14T12:54:06.155</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-125.png">
      <PackagePath>appiconSmallTile.scale-125.png</PackagePath>
      <Modified>2025-06-14T12:54:06.154</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-150.png">
      <PackagePath>appiconSmallTile.scale-150.png</PackagePath>
      <Modified>2025-06-14T12:54:06.153</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-200.png">
      <PackagePath>appiconSmallTile.scale-200.png</PackagePath>
      <Modified>2025-06-14T12:54:06.152</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-400.png">
      <PackagePath>appiconSmallTile.scale-400.png</PackagePath>
      <Modified>2025-06-14T12:54:06.151</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-100.png">
      <PackagePath>appiconStoreLogo.scale-100.png</PackagePath>
      <Modified>2025-06-14T12:54:06.161</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-125.png">
      <PackagePath>appiconStoreLogo.scale-125.png</PackagePath>
      <Modified>2025-06-14T12:54:06.160</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-150.png">
      <PackagePath>appiconStoreLogo.scale-150.png</PackagePath>
      <Modified>2025-06-14T12:54:06.158</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-200.png">
      <PackagePath>appiconStoreLogo.scale-200.png</PackagePath>
      <Modified>2025-06-14T12:54:06.157</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-400.png">
      <PackagePath>appiconStoreLogo.scale-400.png</PackagePath>
      <Modified>2025-06-14T12:54:06.156</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-100.png">
      <PackagePath>appiconWideTile.scale-100.png</PackagePath>
      <Modified>2025-06-14T12:54:06.145</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-125.png">
      <PackagePath>appiconWideTile.scale-125.png</PackagePath>
      <Modified>2025-06-14T12:54:06.144</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-150.png">
      <PackagePath>appiconWideTile.scale-150.png</PackagePath>
      <Modified>2025-06-14T12:54:06.142</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-200.png">
      <PackagePath>appiconWideTile.scale-200.png</PackagePath>
      <Modified>2025-06-14T12:54:06.141</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-400.png">
      <PackagePath>appiconWideTile.scale-400.png</PackagePath>
      <Modified>2025-06-14T12:54:06.140</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-100.png">
      <PackagePath>dotnet_bot.scale-100.png</PackagePath>
      <Modified>2025-06-14T12:54:06.190</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-125.png">
      <PackagePath>dotnet_bot.scale-125.png</PackagePath>
      <Modified>2025-06-14T12:54:06.189</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-150.png">
      <PackagePath>dotnet_bot.scale-150.png</PackagePath>
      <Modified>2025-06-14T12:54:06.188</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-200.png">
      <PackagePath>dotnet_bot.scale-200.png</PackagePath>
      <Modified>2025-06-14T12:54:06.187</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-400.png">
      <PackagePath>dotnet_bot.scale-400.png</PackagePath>
      <Modified>2025-06-14T12:54:06.186</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.2792.45\runtimes\win-x64\native\WebView2Loader.dll">
      <PackagePath>runtimes\win-x64\native\WebView2Loader.dll</PackagePath>
      <Modified>2024-09-15T09:07:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windows.sdk.net.ref\10.0.19041.57\lib\net8.0\Microsoft.Windows.SDK.NET.dll">
      <PackagePath>Microsoft.Windows.SDK.NET.dll</PackagePath>
      <Modified>2024-11-11T17:23:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windows.sdk.net.ref\10.0.19041.57\lib\net8.0\WinRT.Runtime.dll">
      <PackagePath>WinRT.Runtime.dll</PackagePath>
      <Modified>2024-11-11T17:23:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration\9.0.0\lib\net9.0\Microsoft.Extensions.Configuration.dll">
      <PackagePath>Microsoft.Extensions.Configuration.dll</PackagePath>
      <Modified>2024-10-29T02:38:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.abstractions\9.0.0\lib\net9.0\Microsoft.Extensions.Configuration.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.Configuration.Abstractions.dll</PackagePath>
      <Modified>2024-10-29T02:38:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection\9.0.0\lib\net9.0\Microsoft.Extensions.DependencyInjection.dll">
      <PackagePath>Microsoft.Extensions.DependencyInjection.dll</PackagePath>
      <Modified>2024-10-29T02:38:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection.abstractions\9.0.0\lib\net9.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.DependencyInjection.Abstractions.dll</PackagePath>
      <Modified>2024-10-29T02:38:16.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging\9.0.0\lib\net9.0\Microsoft.Extensions.Logging.dll">
      <PackagePath>Microsoft.Extensions.Logging.dll</PackagePath>
      <Modified>2024-10-29T02:38:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.0\lib\net9.0\Microsoft.Extensions.Logging.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.Logging.Abstractions.dll</PackagePath>
      <Modified>2024-10-29T02:38:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.debug\9.0.0\lib\net9.0\Microsoft.Extensions.Logging.Debug.dll">
      <PackagePath>Microsoft.Extensions.Logging.Debug.dll</PackagePath>
      <Modified>2024-10-29T02:38:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.0\lib\net9.0\Microsoft.Extensions.Options.dll">
      <PackagePath>Microsoft.Extensions.Options.dll</PackagePath>
      <Modified>2024-10-29T02:38:24.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.primitives\9.0.0\lib\net9.0\Microsoft.Extensions.Primitives.dll">
      <PackagePath>Microsoft.Extensions.Primitives.dll</PackagePath>
      <Modified>2024-10-29T02:38:00.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.graphics.win2d\1.2.0\lib\net6.0-windows10.0.19041.0\Microsoft.Graphics.Canvas.Interop.dll">
      <PackagePath>Microsoft.Graphics.Canvas.Interop.dll</PackagePath>
      <Modified>2024-03-11T15:36:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.io.recyclablememorystream\3.0.1\lib\net6.0\Microsoft.IO.RecyclableMemoryStream.dll">
      <PackagePath>Microsoft.IO.RecyclableMemoryStream.dll</PackagePath>
      <Modified>2024-06-11T20:23:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.compatibility\9.0.10\lib\net9.0-windows10.0.19041\Microsoft.Maui.Controls.Compatibility.dll">
      <PackagePath>Microsoft.Maui.Controls.Compatibility.dll</PackagePath>
      <Modified>2024-11-12T04:14:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\Microsoft.Maui.Controls.dll">
      <PackagePath>Microsoft.Maui.Controls.dll</PackagePath>
      <Modified>2024-11-12T04:12:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.xaml\9.0.10\lib\net9.0-windows10.0.19041\Microsoft.Maui.Controls.Xaml.dll">
      <PackagePath>Microsoft.Maui.Controls.Xaml.dll</PackagePath>
      <Modified>2024-11-12T04:13:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.core\9.0.10\lib\net9.0-windows10.0.19041\Microsoft.Maui.dll">
      <PackagePath>Microsoft.Maui.dll</PackagePath>
      <Modified>2024-11-12T04:12:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.essentials\9.0.10\lib\net9.0-windows10.0.19041\Microsoft.Maui.Essentials.dll">
      <PackagePath>Microsoft.Maui.Essentials.dll</PackagePath>
      <Modified>2024-11-12T04:12:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.graphics\9.0.10\lib\net9.0-windows10.0.19041\Microsoft.Maui.Graphics.dll">
      <PackagePath>Microsoft.Maui.Graphics.dll</PackagePath>
      <Modified>2024-11-12T04:13:16.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.graphics.win2d.winui.desktop\9.0.10\lib\net9.0-windows10.0.19041\Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll">
      <PackagePath>Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll</PackagePath>
      <Modified>2024-11-12T04:12:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\lib\net6.0-windows10.0.18362.0\Microsoft.InteractiveExperiences.Projection.dll">
      <PackagePath>Microsoft.InteractiveExperiences.Projection.dll</PackagePath>
      <Modified>2024-09-17T04:17:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\lib\net6.0-windows10.0.18362.0\Microsoft.WinUI.dll">
      <PackagePath>Microsoft.WinUI.dll</PackagePath>
      <Modified>2024-09-21T17:55:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AppLifecycle.Projection.dll">
      <PackagePath>Microsoft.Windows.AppLifecycle.Projection.dll</PackagePath>
      <Modified>2024-09-21T17:55:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AppNotifications.Builder.Projection.dll">
      <PackagePath>Microsoft.Windows.AppNotifications.Builder.Projection.dll</PackagePath>
      <Modified>2024-09-21T17:55:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AppNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.AppNotifications.Projection.dll</PackagePath>
      <Modified>2024-09-21T17:55:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll</PackagePath>
      <Modified>2024-09-21T17:55:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.ApplicationModel.Resources.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.Resources.Projection.dll</PackagePath>
      <Modified>2024-09-21T00:49:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll</PackagePath>
      <Modified>2024-09-21T17:55:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.Management.Deployment.Projection.dll">
      <PackagePath>Microsoft.Windows.Management.Deployment.Projection.dll</PackagePath>
      <Modified>2024-09-21T17:55:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.PushNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.PushNotifications.Projection.dll</PackagePath>
      <Modified>2024-09-21T17:55:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.Security.AccessControl.Projection.dll">
      <PackagePath>Microsoft.Windows.Security.AccessControl.Projection.dll</PackagePath>
      <Modified>2024-09-21T17:55:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.Storage.Projection.dll">
      <PackagePath>Microsoft.Windows.Storage.Projection.dll</PackagePath>
      <Modified>2024-09-21T17:55:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.System.Power.Projection.dll">
      <PackagePath>Microsoft.Windows.System.Power.Projection.dll</PackagePath>
      <Modified>2024-09-21T17:55:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.System.Projection.dll">
      <PackagePath>Microsoft.Windows.System.Projection.dll</PackagePath>
      <Modified>2024-09-21T17:55:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.Widgets.Projection.dll">
      <PackagePath>Microsoft.Windows.Widgets.Projection.dll</PackagePath>
      <Modified>2024-08-29T18:48:00.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\lib\net6.0-windows10.0.18362.0\Microsoft.WindowsAppRuntime.Bootstrap.Net.dll">
      <PackagePath>Microsoft.WindowsAppRuntime.Bootstrap.Net.dll</PackagePath>
      <Modified>2024-09-21T17:55:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\ar\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ar\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\ca\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ca\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\cs\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>cs\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:16.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\da\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>da\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\de\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>de\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\el\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>el\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:12:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\es\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>es\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:12:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\fi\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>fi\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:12:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\fr\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>fr\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\he\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>he\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:12:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\hi\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>hi\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:12:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\hr\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>hr\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\hu\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>hu\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\id\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>id\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\it\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>it\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\ja\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ja\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\ko\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ko\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\ms\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ms\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\nb\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>nb\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\nl\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>nl\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\pl\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>pl\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:40.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\pt-BR\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>pt-BR\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:12:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\pt\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>pt\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\ro\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ro\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\ru\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ru\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:12:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\sk\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>sk\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:12:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\sv\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>sv\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\th\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>th\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:40.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\tr\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>tr\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\uk\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>uk\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:38.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\vi\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>vi\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\zh-HK\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>zh-HK\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\zh-Hans\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>zh-Hans\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\zh-Hant\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>zh-Hant\Microsoft.Maui.Controls.resources.dll</PackagePath>
      <Modified>2024-11-12T04:13:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.graphics.win2d\1.2.0\runtimes\win-x64\native\Microsoft.Graphics.Canvas.dll">
      <PackagePath>Microsoft.Graphics.Canvas.dll</PackagePath>
      <Modified>2024-03-11T15:36:40.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.2792.45\runtimes\win-x64\native\WebView2Loader.dll">
      <PackagePath>WebView2Loader.dll</PackagePath>
      <Modified>2024-09-15T09:07:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\runtimes\win-x64\native\Microsoft.WindowsAppRuntime.Bootstrap.dll">
      <PackagePath>Microsoft.WindowsAppRuntime.Bootstrap.dll</PackagePath>
      <Modified>2024-09-21T00:48:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.2792.45\runtimes\win-x64\native_uap\Microsoft.Web.WebView2.Core.dll">
      <PackagePath>Microsoft.Web.WebView2.Core.dll</PackagePath>
      <Modified>2024-09-15T09:10:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.2792.45\lib_manual\net6.0-windows10.0.17763.0\Microsoft.Web.WebView2.Core.Projection.dll">
      <PackagePath>Microsoft.Web.WebView2.Core.Projection.dll</PackagePath>
      <Modified>2024-09-15T09:07:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\bin\Debug\net9.0-windows10.0.19041.0\win10-x64\unitedprint.deps.json">
      <PackagePath>unitedprint.deps.json</PackagePath>
      <Modified>2025-06-14T13:00:30.983</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.compatibility\9.0.10\lib\net9.0-windows10.0.19041\Microsoft.Maui.Controls.Compatibility.pri">
      <PackagePath>Microsoft.Maui.Controls.Compatibility.pri</PackagePath>
      <Modified>2024-11-12T03:53:00.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\Microsoft.Maui.Controls.pri">
      <PackagePath>Microsoft.Maui.Controls.pri</PackagePath>
      <Modified>2024-11-12T03:51:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.core\9.0.10\lib\net9.0-windows10.0.19041\Microsoft.Maui.pri">
      <PackagePath>Microsoft.Maui.pri</PackagePath>
      <Modified>2024-11-12T03:51:00.000</Modified>
    </AppxPackagedFile>
  </ItemGroup>
  <ItemGroup>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.6.msix">
      <Name>Microsoft.WindowsAppRuntime.1.6</Name>
      <Version>6000.266.2241.0</Version>
      <Architecture>x86</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.6, MinVersion = 6000.266.2241.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.6.msix</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.6.msix">
      <Name>Microsoft.WindowsAppRuntime.1.6</Name>
      <Version>6000.266.2241.0</Version>
      <Architecture>win32</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.6, MinVersion = 6000.266.2241.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.6.msix</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\buildTransitive\..\tools\MSIX\win10-x64\Microsoft.WindowsAppRuntime.1.6.msix">
      <Name>Microsoft.WindowsAppRuntime.1.6</Name>
      <Version>6000.266.2241.0</Version>
      <Architecture>x64</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.6, MinVersion = 6000.266.2241.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\buildTransitive\..\tools\MSIX\win10-x64\Microsoft.WindowsAppRuntime.1.6.msix</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\buildTransitive\..\tools\MSIX\win10-arm64\Microsoft.WindowsAppRuntime.1.6.msix">
      <Name>Microsoft.WindowsAppRuntime.1.6</Name>
      <Version>6000.266.2241.0</Version>
      <Architecture>arm64</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.6, MinVersion = 6000.266.2241.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\buildTransitive\..\tools\MSIX\win10-arm64\Microsoft.WindowsAppRuntime.1.6.msix</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
  </ItemGroup>
</Project>