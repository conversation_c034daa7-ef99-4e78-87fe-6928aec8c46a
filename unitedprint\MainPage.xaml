﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="unitedprint.MainPage"
             BackgroundColor="White"
             Shell.NavBarIsVisible="False"
             Shell.TabBarIsVisible="False">

    <Grid BackgroundColor="White">

        <!-- Main Content -->
        <ScrollView>
            <Grid Padding="40,60,40,40">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!-- EDIS Logo -->
                <StackLayout Grid.Row="0" HorizontalOptions="Center" Margin="0,0,0,40">
                    <Image Source="edis_logo.png"
                           HeightRequest="120"
                           Aspect="AspectFit"
                           HorizontalOptions="Center" />
                </StackLayout>

                <!-- Logo above search -->
                <StackLayout Grid.Row="1" HorizontalOptions="Center" Margin="0,0,0,20">
                    <Image Source="search_logo.png"
                           HeightRequest="80"
                           Aspect="AspectFit"
                           HorizontalOptions="Center" />
                </StackLayout>

                <!-- Input Field -->
                <StackLayout Grid.Row="2" Margin="20,0,20,20">
                    <Entry x:Name="InputEntry"
                           Placeholder="QR Code Or Mobile Number"
                           FontSize="16"
                           TextColor="Black"
                           BackgroundColor="White"
                           HorizontalOptions="FillAndExpand"
                           Completed="OnEntryCompleted"
                           ReturnType="Search"
                           ClearButtonVisibility="WhileEditing" />
                </StackLayout>

                <!-- Label for Input -->
                <Label Grid.Row="3"
                       Text="QR Code Or Mobile Number"
                       FontSize="18"
                       TextColor="Black"
                       HorizontalOptions="Center"
                       Margin="0,0,0,30" />

                <!-- Numeric Keypad -->
                <Grid Grid.Row="4" HorizontalOptions="Center" VerticalOptions="Center">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="60" />
                        <RowDefinition Height="60" />
                        <RowDefinition Height="60" />
                        <RowDefinition Height="60" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="80" />
                    </Grid.ColumnDefinitions>

                    <!-- Row 1: 1, 2, 3 -->
                    <Button Grid.Row="0" Grid.Column="0" Text="1" Clicked="OnNumberClicked"
                            BackgroundColor="#1976D2" TextColor="White" CornerRadius="10" Margin="5" />
                    <Button Grid.Row="0" Grid.Column="1" Text="2" Clicked="OnNumberClicked"
                            BackgroundColor="#1976D2" TextColor="White" CornerRadius="10" Margin="5" />
                    <Button Grid.Row="0" Grid.Column="2" Text="3" Clicked="OnNumberClicked"
                            BackgroundColor="#1976D2" TextColor="White" CornerRadius="10" Margin="5" />

                    <!-- Row 2: 4, 5, 6 -->
                    <Button Grid.Row="1" Grid.Column="0" Text="4" Clicked="OnNumberClicked"
                            BackgroundColor="#1976D2" TextColor="White" CornerRadius="10" Margin="5" />
                    <Button Grid.Row="1" Grid.Column="1" Text="5" Clicked="OnNumberClicked"
                            BackgroundColor="#1976D2" TextColor="White" CornerRadius="10" Margin="5" />
                    <Button Grid.Row="1" Grid.Column="2" Text="6" Clicked="OnNumberClicked"
                            BackgroundColor="#1976D2" TextColor="White" CornerRadius="10" Margin="5" />

                    <!-- Row 3: 7, 8, 9 -->
                    <Button Grid.Row="2" Grid.Column="0" Text="7" Clicked="OnNumberClicked"
                            BackgroundColor="#1976D2" TextColor="White" CornerRadius="10" Margin="5" />
                    <Button Grid.Row="2" Grid.Column="1" Text="8" Clicked="OnNumberClicked"
                            BackgroundColor="#1976D2" TextColor="White" CornerRadius="10" Margin="5" />
                    <Button Grid.Row="2" Grid.Column="2" Text="9" Clicked="OnNumberClicked"
                            BackgroundColor="#1976D2" TextColor="White" CornerRadius="10" Margin="5" />

                    <!-- Row 4: Reset, 0, Delete -->
                    <Button Grid.Row="3" Grid.Column="0" Text="Reset" Clicked="OnResetClicked"
                            BackgroundColor="#F44336" TextColor="White" CornerRadius="10" Margin="5" FontSize="12" />
                    <Button Grid.Row="3" Grid.Column="1" Text="0" Clicked="OnNumberClicked"
                            BackgroundColor="#1976D2" TextColor="White" CornerRadius="10" Margin="5" />
                    <Button Grid.Row="3" Grid.Column="2" Text="⌫" Clicked="OnDeleteClicked"
                            BackgroundColor="#F44336" TextColor="White" CornerRadius="10" Margin="5" FontSize="18" />
                </Grid>

                <!-- Search Button -->
                <Button Grid.Row="6"
                        Text="Search"
                        BackgroundColor="#4CAF50"
                        TextColor="White"
                        FontSize="18"
                        FontAttributes="Bold"
                        CornerRadius="25"
                        HeightRequest="50"
                        Margin="0,40,0,0"
                        Clicked="OnSearchClicked" />

                <!-- Bottom Logos -->
                <StackLayout Grid.Row="6"
                             Orientation="Horizontal"
                             HorizontalOptions="Start"
                             VerticalOptions="End"
                             Margin="0,20,0,0">
                    <Image Source="edis_small_logo.png"
                           WidthRequest="60"
                           HeightRequest="60"
                           Aspect="AspectFit"
                           Margin="0,0,15,0" />
                    <Image Source="eia_logo.png"
                           WidthRequest="60"
                           HeightRequest="60"
                           Aspect="AspectFit" />
                </StackLayout>
            </Grid>
        </ScrollView>
    </Grid>

</ContentPage>
