<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="unitedprint.StudentSelectionPage"
             BackgroundColor="White"
             Shell.NavBarIsVisible="False"
             Shell.TabBarIsVisible="False">

    <Grid BackgroundColor="White">
        <!-- Main Content -->
        <ScrollView>
            <Grid Padding="30,60,30,40">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!-- EDIS Logo -->
                <StackLayout Grid.Row="0" HorizontalOptions="Center" Margin="0,0,0,30">
                    <Image Source="edis_logo.png" 
                           HeightRequest="100" 
                           Aspect="AspectFit" 
                           HorizontalOptions="Center" />
                </StackLayout>

                <!-- Title -->
                <Label Grid.Row="1" 
                       Text="Multiple Registrations Found" 
                       FontSize="24" 
                       FontAttributes="Bold"
                       TextColor="#1976D2" 
                       HorizontalOptions="Center" 
                       Margin="0,0,0,10" />

                <!-- Subtitle -->
                <Label Grid.Row="2" 
                       Text="Please select the student:" 
                       FontSize="18" 
                       TextColor="#666666" 
                       HorizontalOptions="Center" 
                       Margin="0,0,0,30" />

                <!-- Students List -->
                <StackLayout Grid.Row="3" x:Name="StudentsContainer" Spacing="15">
                    <!-- Student cards will be added programmatically -->
                </StackLayout>

                <!-- Cancel Button -->
                <Button Grid.Row="4" 
                        Text="Cancel" 
                        BackgroundColor="#F44336" 
                        TextColor="White" 
                        FontSize="16" 
                        FontAttributes="Bold"
                        CornerRadius="25" 
                        HeightRequest="50" 
                        Margin="0,30,0,0"
                        Clicked="OnCancelClicked" />
            </Grid>
        </ScrollView>
    </Grid>

</ContentPage>
