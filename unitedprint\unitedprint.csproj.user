﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <IsFirstTimeProjectOpen>False</IsFirstTimeProjectOpen>
    <ActiveDebugFramework>net9.0-windows10.0.19041.0</ActiveDebugFramework>
    <ActiveDebugProfile>Windows Machine</ActiveDebugProfile>
    <SelectedPlatformGroup>Emulator</SelectedPlatformGroup>
  </PropertyGroup>
  <PropertyGroup Condition="'$(TargetPlatformIdentifier)'=='iOS'">
    <RuntimeIdentifier>iossimulator-x64</RuntimeIdentifier>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net9.0-android|AnyCPU'">
    <DebuggerFlavor>ProjectDebugger</DebuggerFlavor>
  </PropertyGroup>
  <ItemGroup>
    <None Update="App.xaml">
      <SubType>Designer</SubType>
    </None>
    <None Update="AppShell.xaml">
      <SubType>Designer</SubType>
    </None>
    <None Update="MainPage.xaml">
      <SubType>Designer</SubType>
    </None>
    <None Update="Platforms\Windows\App.xaml">
      <SubType>Designer</SubType>
    </None>
    <None Update="Platforms\Windows\Package.appxmanifest">
      <SubType>Designer</SubType>
    </None>
    <None Update="Resources\Styles\Colors.xaml">
      <SubType>Designer</SubType>
    </None>
    <None Update="Resources\Styles\Styles.xaml">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
</Project>