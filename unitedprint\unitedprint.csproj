﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">net9.0-windows10.0.19041.0</TargetFrameworks>
		<TargetFrameworks Condition="!$([MSBuild]::IsOSPlatform('windows'))">net9.0-android;net9.0-ios;net9.0-maccatalyst</TargetFrameworks>
		<!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
		<!-- <TargetFrameworks>$(TargetFrameworks);net8.0-tizen</TargetFrameworks> -->

		<!-- Note for MacCatalyst:
		The default runtime is maccatalyst-x64, except in Release config, in which case the default is maccatalyst-x64;maccatalyst-arm64.
		When specifying both architectures, use the plural <RuntimeIdentifiers> instead of the singular <RuntimeIdentifier>.
		The Mac App Store will NOT accept apps with ONLY maccatalyst-arm64 indicated;
		either BOTH runtimes must be indicated or ONLY macatalyst-x64. -->
		<!-- For example: <RuntimeIdentifiers>maccatalyst-x64;maccatalyst-arm64</RuntimeIdentifiers> -->

		<OutputType>Exe</OutputType>
		<RootNamespace>unitedprint</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<!-- Display name -->
		<ApplicationTitle>unitedprint</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.companyname.unitedprint</ApplicationId>

		<!-- Versions -->
		<ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
		<ApplicationVersion>1</ApplicationVersion>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">11.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">13.1</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
		<TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion>
	</PropertyGroup>

	<ItemGroup>
		<!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

		<!-- Images -->
		<MauiImage Include="Resources\Images\dotnet_bot.png" />
		<MauiImage Include="Resources\Images\edis_logo.png" />
		<MauiImage Include="Resources\Images\edis_small_logo.png" />
		<MauiImage Include="Resources\Images\eia_logo.png" />
		<MauiImage Include="Resources\Images\search_logo.png" />
		<MauiImage Include="Resources\Images\image_one.png" />
		<MauiImage Include="Resources\Images\image_four.png" />
		<MauiImage Include="Resources\Images\image_five.png" />
		<MauiImage Include="Resources\Images\image_fivehundred.jpg" />
		<MauiImage Include="Resources\Images\logo_two.png" />
		<MauiImage Include="Resources\Images\logo_three.png" />
		<MauiImage Include="Resources\Images\eia_logo_copy.png" />

		<MauiImage Update="Resources\Images\dotnet_bot.png" Resize="True" BaseSize="300,185" />
		<MauiImage Update="Resources\Images\edis_logo.png" Resize="True" BaseSize="300,120" />
		<MauiImage Update="Resources\Images\edis_small_logo.png" Resize="True" BaseSize="60,60" />
		<MauiImage Update="Resources\Images\eia_logo.png" Resize="True" BaseSize="60,60" />
		<MauiImage Update="Resources\Images\search_logo.png" Resize="True" BaseSize="200,80" />

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Maui.Controls" Version="9.0.10" />
		<PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="9.0.10" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.0" />
		<PackageReference Include="System.Text.Json" Version="9.0.0" />
		<PackageReference Include="ZXing.Net" Version="0.16.9" />
		<PackageReference Include="Microsoft.Maui.Graphics.Skia" Version="9.0.10" />
	</ItemGroup>

	<ItemGroup Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">
		<PackageReference Include="System.Drawing.Common" Version="9.0.0" />
		<!-- Add Zebra SDK reference here if you have the NuGet package -->
		<!-- <PackageReference Include="Zebra.Printer.SDK" Version="x.x.x" /> -->
	</ItemGroup>

</Project>
