{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\united2026\\unitedprint\\unitedprint\\unitedprint.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\united2026\\unitedprint\\unitedprint\\unitedprint.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\united2026\\unitedprint\\unitedprint\\unitedprint.csproj", "projectName": "unitedprint", "projectPath": "C:\\Users\\<USER>\\Desktop\\united2026\\unitedprint\\unitedprint\\unitedprint.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\united2026\\unitedprint\\unitedprint\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows10.0.19041.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows10.0.19041": {"targetAlias": "net9.0-windows10.0.19041.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows10.0.19041": {"targetAlias": "net9.0-windows10.0.19041.0", "dependencies": {"Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.10, )"}, "Microsoft.Maui.Controls.Compatibility": {"target": "Package", "version": "[9.0.10, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.Windows.SDK.NET.Ref", "version": "[10.0.19041.57, 10.0.19041.57]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.Windows.SDK.NET.Ref.Windows": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win10-x64": {"#import": []}}}}}