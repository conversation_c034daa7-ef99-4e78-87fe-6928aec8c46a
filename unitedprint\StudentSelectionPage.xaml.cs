using Microsoft.Maui.Controls;
using System.Text.Json;

namespace unitedprint
{
    public partial class StudentSelectionPage : ContentPage
    {
        private List<JsonElement> _students;
        private string _phoneNumber;

        public StudentSelectionPage()
        {
            InitializeComponent();
        }

        public StudentSelectionPage(List<JsonElement> students, string phoneNumber) : this()
        {
            _students = students;
            _phoneNumber = phoneNumber;
            LoadStudents();
        }

        private void LoadStudents()
        {
            try
            {
                StudentsContainer.Children.Clear();

                for (int i = 0; i < _students.Count; i++)
                {
                    var student = _students[i];
                    var studentCard = CreateStudentCard(student, i);
                    StudentsContainer.Children.Add(studentCard);
                }
            }
            catch (Exception ex)
            {
                DisplayAlert("Error", $"Failed to load students: {ex.Message}", "OK");
            }
        }

        private Frame CreateStudentCard(JsonElement student, int index)
        {
            // Extract only student name
            string studentName = "Unknown Student";
            if (student.TryGetProperty("student_name", out var nameElement))
                studentName = nameElement.GetString() ?? "Unknown Student";

            // Create the card - simplified design
            var frame = new Frame
            {
                BackgroundColor = Colors.White,
                BorderColor = Color.FromArgb("#E0E0E0"),
                CornerRadius = 15,
                Padding = new Thickness(25, 20),
                Margin = new Thickness(0, 8),
                HasShadow = true
            };

            // Only student name label
            var nameLabel = new Label
            {
                Text = studentName,
                FontSize = 22,
                FontAttributes = FontAttributes.Bold,
                TextColor = Color.FromArgb("#1976D2"),
                HorizontalOptions = LayoutOptions.Center,
                VerticalOptions = LayoutOptions.Center
            };

            frame.Content = nameLabel;

            // Add tap gesture with visual feedback
            var tapGesture = new TapGestureRecognizer();
            tapGesture.Tapped += async (sender, e) =>
            {
                // Visual feedback on tap
                frame.BackgroundColor = Color.FromArgb("#F0F8FF");
                frame.BorderColor = Color.FromArgb("#1976D2");

                // Small delay for visual feedback
                await Task.Delay(100);

                // Reset colors
                frame.BackgroundColor = Colors.White;
                frame.BorderColor = Color.FromArgb("#E0E0E0");

                // Handle selection
                OnStudentSelected(index);
            };
            frame.GestureRecognizers.Add(tapGesture);

            return frame;
        }

        private async void OnStudentSelected(int index)
        {
            try
            {
                if (index >= 0 && index < _students.Count)
                {
                    var selectedStudent = _students[index];
                    
                    // Navigate to results page with selected student
                    var resultsPage = new ResultsPage(selectedStudent, _phoneNumber);
                    await Shell.Current.Navigation.PushAsync(resultsPage);
                    
                    // Remove this page from navigation stack
                    Shell.Current.Navigation.RemovePage(this);
                }
            }
            catch (Exception ex)
            {
                await DisplayAlert("Error", $"Failed to select student: {ex.Message}", "OK");
            }
        }

        private async void OnCancelClicked(object sender, EventArgs e)
        {
            // Go back to main page
            await Shell.Current.Navigation.PopAsync();
        }
    }
}
