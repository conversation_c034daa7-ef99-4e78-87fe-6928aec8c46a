using Microsoft.Maui.Controls;
using System.Text.Json;

namespace unitedprint
{
    public partial class StudentSelectionPage : ContentPage
    {
        private List<JsonElement> _students;
        private string _phoneNumber;

        public StudentSelectionPage()
        {
            InitializeComponent();
        }

        public StudentSelectionPage(List<JsonElement> students, string phoneNumber) : this()
        {
            _students = students;
            _phoneNumber = phoneNumber;
            LoadStudents();
        }

        private void LoadStudents()
        {
            try
            {
                StudentsContainer.Children.Clear();

                for (int i = 0; i < _students.Count; i++)
                {
                    var student = _students[i];
                    var studentCard = CreateStudentCard(student, i);
                    StudentsContainer.Children.Add(studentCard);
                }
            }
            catch (Exception ex)
            {
                DisplayAlert("Error", $"Failed to load students: {ex.Message}", "OK");
            }
        }

        private Frame CreateStudentCard(JsonElement student, int index)
        {
            // Extract student information
            string studentName = "Unknown Student";
            string studentPhone = "";
            string studentEmail = "";
            string city = "";
            string major = "";

            if (student.TryGetProperty("student_name", out var nameElement))
                studentName = nameElement.GetString() ?? "Unknown Student";

            if (student.TryGetProperty("student_phone", out var phoneElement))
                studentPhone = phoneElement.GetString() ?? "";

            if (student.TryGetProperty("student_email", out var emailElement))
                studentEmail = emailElement.GetString() ?? "";

            if (student.TryGetProperty("residence_city", out var cityElement))
                city = cityElement.GetString() ?? "";

            if (student.TryGetProperty("desired_major", out var majorElement))
                major = majorElement.GetString() ?? "";

            // Create the card
            var frame = new Frame
            {
                BackgroundColor = Colors.White,
                BorderColor = Color.FromArgb("#E0E0E0"),
                CornerRadius = 15,
                Padding = new Thickness(20),
                Margin = new Thickness(0, 5),
                HasShadow = true
            };

            var stackLayout = new StackLayout
            {
                Spacing = 8
            };

            // Student name (main)
            var nameLabel = new Label
            {
                Text = studentName,
                FontSize = 20,
                FontAttributes = FontAttributes.Bold,
                TextColor = Color.FromArgb("#1976D2"),
                HorizontalOptions = LayoutOptions.Start
            };

            // Phone number
            var phoneLabel = new Label
            {
                Text = $"📞 {studentPhone}",
                FontSize = 16,
                TextColor = Color.FromArgb("#333333"),
                HorizontalOptions = LayoutOptions.Start
            };

            // Email (if available)
            if (!string.IsNullOrEmpty(studentEmail))
            {
                var emailLabel = new Label
                {
                    Text = $"✉️ {studentEmail}",
                    FontSize = 14,
                    TextColor = Color.FromArgb("#666666"),
                    HorizontalOptions = LayoutOptions.Start
                };
                stackLayout.Children.Add(emailLabel);
            }

            // City and Major in one line (if available)
            var detailsText = "";
            if (!string.IsNullOrEmpty(city))
                detailsText += $"📍 {city}";
            
            if (!string.IsNullOrEmpty(major))
            {
                if (!string.IsNullOrEmpty(detailsText))
                    detailsText += " • ";
                detailsText += $"🎓 {major}";
            }

            if (!string.IsNullOrEmpty(detailsText))
            {
                var detailsLabel = new Label
                {
                    Text = detailsText,
                    FontSize = 12,
                    TextColor = Color.FromArgb("#888888"),
                    HorizontalOptions = LayoutOptions.Start
                };
                stackLayout.Children.Add(detailsLabel);
            }

            stackLayout.Children.Insert(0, nameLabel);
            stackLayout.Children.Insert(1, phoneLabel);

            frame.Content = stackLayout;

            // Add tap gesture with visual feedback
            var tapGesture = new TapGestureRecognizer();
            tapGesture.Tapped += async (sender, e) =>
            {
                // Visual feedback on tap
                frame.BackgroundColor = Color.FromArgb("#F0F8FF");
                frame.BorderColor = Color.FromArgb("#1976D2");

                // Small delay for visual feedback
                await Task.Delay(100);

                // Reset colors
                frame.BackgroundColor = Colors.White;
                frame.BorderColor = Color.FromArgb("#E0E0E0");

                // Handle selection
                OnStudentSelected(index);
            };
            frame.GestureRecognizers.Add(tapGesture);

            return frame;
        }

        private async void OnStudentSelected(int index)
        {
            try
            {
                if (index >= 0 && index < _students.Count)
                {
                    var selectedStudent = _students[index];
                    
                    // Navigate to results page with selected student
                    var resultsPage = new ResultsPage(selectedStudent, _phoneNumber);
                    await Shell.Current.Navigation.PushAsync(resultsPage);
                    
                    // Remove this page from navigation stack
                    Shell.Current.Navigation.RemovePage(this);
                }
            }
            catch (Exception ex)
            {
                await DisplayAlert("Error", $"Failed to select student: {ex.Message}", "OK");
            }
        }

        private async void OnCancelClicked(object sender, EventArgs e)
        {
            // Go back to main page
            await Shell.Current.Navigation.PopAsync();
        }
    }
}
