# PowerShell script to fix MAUI image naming issues
Write-Host "Fixing MAUI image file names..." -ForegroundColor Green

$imagesPath = "unitedprint\Resources\Images"

if (Test-Path $imagesPath) {
    Write-Host "Found Images folder: $imagesPath" -ForegroundColor Yellow
    
    # Define the mapping of old names to new MAUI-compliant names
    $renameMap = @{
        "1.png" = "image_one.png"
        "2-03.png" = "search_logo.png"
        "2-04.png" = "logo_two.png"
        "2-05.png" = "logo_three.png"
        "4.png" = "image_four.png"
        "5.png" = "image_five.png"
        "500.jpg" = "image_fivehundred.jpg"
        "EDIS-e-LOGO.png" = "edis_logo.png"
        "yn-03.png" = "edis_small_logo.png"
        "za-02.png" = "eia_logo.png"
        "za-02 - Copy.png" = "eia_logo_copy.png"
    }
    
    # Rename files
    foreach ($oldName in $renameMap.Keys) {
        $oldPath = Join-Path $imagesPath $oldName
        $newName = $renameMap[$oldName]
        $newPath = Join-Path $imagesPath $newName
        
        if (Test-Path $oldPath) {
            try {
                Rename-Item -Path $oldPath -NewName $newName -Force
                Write-Host "✓ Renamed: $oldName → $newName" -ForegroundColor Green
            }
            catch {
                Write-Host "✗ Failed to rename: $oldName → $newName" -ForegroundColor Red
                Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
        else {
            Write-Host "- File not found: $oldName" -ForegroundColor Gray
        }
    }
    
    Write-Host "`nFile renaming completed!" -ForegroundColor Green
    Write-Host "`nCurrent files in Images folder:" -ForegroundColor Yellow
    Get-ChildItem $imagesPath | ForEach-Object { Write-Host "  $($_.Name)" }
    
} else {
    Write-Host "Images folder not found: $imagesPath" -ForegroundColor Red
}

Write-Host "`nPress any key to continue..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
