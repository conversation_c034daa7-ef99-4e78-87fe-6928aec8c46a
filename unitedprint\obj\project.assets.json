{"version": 3, "targets": {"net9.0-windows10.0.19041": {"Microsoft.Extensions.Configuration/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Debug/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Options/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/9.0.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Graphics.Win2D/1.2.0": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK": "1.5.240227000"}, "compile": {"lib/net6.0-windows10.0.19041.0/Microsoft.Graphics.Canvas.Interop.dll": {}}, "runtime": {"lib/net6.0-windows10.0.19041.0/Microsoft.Graphics.Canvas.Interop.dll": {}}, "build": {"build/net6.0-windows10.0.19041.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/Microsoft.Graphics.Canvas.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.Graphics.Canvas.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.Graphics.Canvas.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"type": "package", "compile": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "Microsoft.Maui.Controls/9.0.10": {"type": "package", "dependencies": {"Microsoft.Maui.Controls.Build.Tasks": "9.0.10", "Microsoft.Maui.Controls.Core": "9.0.10", "Microsoft.Maui.Controls.Xaml": "9.0.10", "Microsoft.Maui.Resizetizer": "9.0.10"}}, "Microsoft.Maui.Controls.Build.Tasks/9.0.10": {"type": "package", "dependencies": {"Microsoft.Maui.Controls.Core": "9.0.10", "Microsoft.Maui.Controls.Xaml": "9.0.10"}, "build": {"buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Controls.Build.Tasks.props": {}, "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Controls.Build.Tasks.targets": {}}}, "Microsoft.Maui.Controls.Compatibility/9.0.10": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Maui.Controls.Core": "9.0.10", "Microsoft.Maui.Controls.Xaml": "9.0.10"}, "compile": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Compatibility.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Compatibility.dll": {"related": ".pdb;.pri;.xml"}}}, "Microsoft.Maui.Controls.Core/9.0.10": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Maui.Core": "9.0.10"}, "compile": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.dll": {"related": ".pdb;.pri;.xml"}}, "resource": {"lib/net9.0-windows10.0.19041/ar/Microsoft.Maui.Controls.resources.dll": {"locale": "ar"}, "lib/net9.0-windows10.0.19041/ca/Microsoft.Maui.Controls.resources.dll": {"locale": "ca"}, "lib/net9.0-windows10.0.19041/cs/Microsoft.Maui.Controls.resources.dll": {"locale": "cs"}, "lib/net9.0-windows10.0.19041/da/Microsoft.Maui.Controls.resources.dll": {"locale": "da"}, "lib/net9.0-windows10.0.19041/de/Microsoft.Maui.Controls.resources.dll": {"locale": "de"}, "lib/net9.0-windows10.0.19041/el/Microsoft.Maui.Controls.resources.dll": {"locale": "el"}, "lib/net9.0-windows10.0.19041/es/Microsoft.Maui.Controls.resources.dll": {"locale": "es"}, "lib/net9.0-windows10.0.19041/fi/Microsoft.Maui.Controls.resources.dll": {"locale": "fi"}, "lib/net9.0-windows10.0.19041/fr/Microsoft.Maui.Controls.resources.dll": {"locale": "fr"}, "lib/net9.0-windows10.0.19041/he/Microsoft.Maui.Controls.resources.dll": {"locale": "he"}, "lib/net9.0-windows10.0.19041/hi/Microsoft.Maui.Controls.resources.dll": {"locale": "hi"}, "lib/net9.0-windows10.0.19041/hr/Microsoft.Maui.Controls.resources.dll": {"locale": "hr"}, "lib/net9.0-windows10.0.19041/hu/Microsoft.Maui.Controls.resources.dll": {"locale": "hu"}, "lib/net9.0-windows10.0.19041/id/Microsoft.Maui.Controls.resources.dll": {"locale": "id"}, "lib/net9.0-windows10.0.19041/it/Microsoft.Maui.Controls.resources.dll": {"locale": "it"}, "lib/net9.0-windows10.0.19041/ja/Microsoft.Maui.Controls.resources.dll": {"locale": "ja"}, "lib/net9.0-windows10.0.19041/ko/Microsoft.Maui.Controls.resources.dll": {"locale": "ko"}, "lib/net9.0-windows10.0.19041/ms/Microsoft.Maui.Controls.resources.dll": {"locale": "ms"}, "lib/net9.0-windows10.0.19041/nb/Microsoft.Maui.Controls.resources.dll": {"locale": "nb"}, "lib/net9.0-windows10.0.19041/nl/Microsoft.Maui.Controls.resources.dll": {"locale": "nl"}, "lib/net9.0-windows10.0.19041/pl/Microsoft.Maui.Controls.resources.dll": {"locale": "pl"}, "lib/net9.0-windows10.0.19041/pt-BR/Microsoft.Maui.Controls.resources.dll": {"locale": "pt-BR"}, "lib/net9.0-windows10.0.19041/pt/Microsoft.Maui.Controls.resources.dll": {"locale": "pt"}, "lib/net9.0-windows10.0.19041/ro/Microsoft.Maui.Controls.resources.dll": {"locale": "ro"}, "lib/net9.0-windows10.0.19041/ru/Microsoft.Maui.Controls.resources.dll": {"locale": "ru"}, "lib/net9.0-windows10.0.19041/sk/Microsoft.Maui.Controls.resources.dll": {"locale": "sk"}, "lib/net9.0-windows10.0.19041/sv/Microsoft.Maui.Controls.resources.dll": {"locale": "sv"}, "lib/net9.0-windows10.0.19041/th/Microsoft.Maui.Controls.resources.dll": {"locale": "th"}, "lib/net9.0-windows10.0.19041/tr/Microsoft.Maui.Controls.resources.dll": {"locale": "tr"}, "lib/net9.0-windows10.0.19041/uk/Microsoft.Maui.Controls.resources.dll": {"locale": "uk"}, "lib/net9.0-windows10.0.19041/vi/Microsoft.Maui.Controls.resources.dll": {"locale": "vi"}, "lib/net9.0-windows10.0.19041/zh-HK/Microsoft.Maui.Controls.resources.dll": {"locale": "zh-HK"}, "lib/net9.0-windows10.0.19041/zh-Hans/Microsoft.Maui.Controls.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0-windows10.0.19041/zh-Hant/Microsoft.Maui.Controls.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Maui.Controls.Xaml/9.0.10": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Maui.Controls.Core": "9.0.10", "Microsoft.Maui.Core": "9.0.10"}, "compile": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Xaml.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Xaml.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Maui.Core/9.0.10": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Graphics.Win2D": "1.2.0", "Microsoft.Maui.Essentials": "9.0.10", "Microsoft.Maui.Graphics": "9.0.10", "Microsoft.Maui.Graphics.Win2D.WinUI.Desktop": "9.0.10", "Microsoft.Web.WebView2": "1.0.2792.45", "Microsoft.Windows.SDK.BuildTools": "10.0.22621.756", "Microsoft.WindowsAppSDK": "1.6.240923002"}, "compile": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.dll": {"related": ".pdb;.pri;.xml"}}, "build": {"buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Core.props": {}, "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Core.targets": {}}}, "Microsoft.Maui.Essentials/9.0.10": {"type": "package", "dependencies": {"Microsoft.Maui.Graphics": "9.0.10"}, "compile": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Essentials.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Essentials.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Maui.Graphics/9.0.10": {"type": "package", "dependencies": {"Microsoft.Graphics.Win2D": "1.2.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.1"}, "compile": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Maui.Graphics.Skia/9.0.10": {"type": "package", "dependencies": {"Microsoft.Maui.Graphics": "9.0.10", "SkiaSharp": "2.88.8", "SkiaSharp.Views.WinUI": "2.88.8"}, "compile": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.Skia.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.Skia.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Maui.Graphics.Win2D.WinUI.Desktop/9.0.10": {"type": "package", "dependencies": {"Microsoft.Graphics.Win2D": "1.2.0", "Microsoft.Maui.Graphics": "9.0.10"}, "compile": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll": {"related": ".pdb"}}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll": {"related": ".pdb"}}}, "Microsoft.Maui.Resizetizer/9.0.10": {"type": "package", "build": {"buildTransitive/Microsoft.Maui.Resizetizer.props": {}, "buildTransitive/Microsoft.Maui.Resizetizer.targets": {}}}, "Microsoft.Web.WebView2/1.0.2792.45": {"type": "package", "build": {"buildTransitive/Microsoft.Web.WebView2.targets": {}}, "runtimeTargets": {"runtimes/win-arm64/native/WebView2Loader.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/WebView2Loader.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/WebView2Loader.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.Win32.SystemEvents/9.0.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Windows.SDK.BuildTools/10.0.22621.756": {"type": "package", "build": {"buildTransitive/Microsoft.Windows.SDK.BuildTools.props": {}, "buildTransitive/Microsoft.Windows.SDK.BuildTools.targets": {}}}, "Microsoft.WindowsAppSDK/1.6.240923002": {"type": "package", "dependencies": {"Microsoft.Web.WebView2": "1.0.2651.64", "Microsoft.Windows.SDK.BuildTools": "10.0.22621.756"}, "compile": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "runtime": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.targets": {}}, "runtimeTargets": {"runtimes/win-arm64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win-x86"}}}, "SkiaSharp/2.88.8": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.8", "SkiaSharp.NativeAssets.macOS": "2.88.8"}, "compile": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.NativeAssets.macOS/2.88.8": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "SkiaSharp.Views.WinUI/2.88.8": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK": "1.1.5", "SkiaSharp": "2.88.8"}, "compile": {"lib/net6.0-windows10.0.19041.0/SkiaSharp.Views.Windows.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0-windows10.0.19041.0/SkiaSharp.Views.Windows.dll": {"related": ".pdb;.xml"}}}, "System.Drawing.Common/9.0.0": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "9.0.0"}, "compile": {"lib/net9.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}, "lib/net9.0/System.Private.Windows.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}, "lib/net9.0/System.Private.Windows.Core.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Text.Json/9.0.0": {"type": "package", "compile": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/System.Text.Json.targets": {}}}, "ZXing.Net/0.16.9": {"type": "package", "compile": {"lib/net7.0/zxing.dll": {"related": ".XML"}}, "runtime": {"lib/net7.0/zxing.dll": {"related": ".XML"}}}}, "net9.0-windows10.0.19041/win10-x64": {"Microsoft.Extensions.Configuration/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Debug/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Options/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/9.0.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Graphics.Win2D/1.2.0": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK": "1.5.240227000"}, "compile": {"lib/net6.0-windows10.0.19041.0/Microsoft.Graphics.Canvas.Interop.dll": {}}, "runtime": {"lib/net6.0-windows10.0.19041.0/Microsoft.Graphics.Canvas.Interop.dll": {}}, "native": {"runtimes/win-x64/native/Microsoft.Graphics.Canvas.dll": {}}, "build": {"build/net6.0-windows10.0.19041.0/_._": {}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"type": "package", "compile": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "Microsoft.Maui.Controls/9.0.10": {"type": "package", "dependencies": {"Microsoft.Maui.Controls.Build.Tasks": "9.0.10", "Microsoft.Maui.Controls.Core": "9.0.10", "Microsoft.Maui.Controls.Xaml": "9.0.10", "Microsoft.Maui.Resizetizer": "9.0.10"}}, "Microsoft.Maui.Controls.Build.Tasks/9.0.10": {"type": "package", "dependencies": {"Microsoft.Maui.Controls.Core": "9.0.10", "Microsoft.Maui.Controls.Xaml": "9.0.10"}, "build": {"buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Controls.Build.Tasks.props": {}, "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Controls.Build.Tasks.targets": {}}}, "Microsoft.Maui.Controls.Compatibility/9.0.10": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Maui.Controls.Core": "9.0.10", "Microsoft.Maui.Controls.Xaml": "9.0.10"}, "compile": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Compatibility.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Compatibility.dll": {"related": ".pdb;.pri;.xml"}}}, "Microsoft.Maui.Controls.Core/9.0.10": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Maui.Core": "9.0.10"}, "compile": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.dll": {"related": ".pdb;.pri;.xml"}}, "resource": {"lib/net9.0-windows10.0.19041/ar/Microsoft.Maui.Controls.resources.dll": {"locale": "ar"}, "lib/net9.0-windows10.0.19041/ca/Microsoft.Maui.Controls.resources.dll": {"locale": "ca"}, "lib/net9.0-windows10.0.19041/cs/Microsoft.Maui.Controls.resources.dll": {"locale": "cs"}, "lib/net9.0-windows10.0.19041/da/Microsoft.Maui.Controls.resources.dll": {"locale": "da"}, "lib/net9.0-windows10.0.19041/de/Microsoft.Maui.Controls.resources.dll": {"locale": "de"}, "lib/net9.0-windows10.0.19041/el/Microsoft.Maui.Controls.resources.dll": {"locale": "el"}, "lib/net9.0-windows10.0.19041/es/Microsoft.Maui.Controls.resources.dll": {"locale": "es"}, "lib/net9.0-windows10.0.19041/fi/Microsoft.Maui.Controls.resources.dll": {"locale": "fi"}, "lib/net9.0-windows10.0.19041/fr/Microsoft.Maui.Controls.resources.dll": {"locale": "fr"}, "lib/net9.0-windows10.0.19041/he/Microsoft.Maui.Controls.resources.dll": {"locale": "he"}, "lib/net9.0-windows10.0.19041/hi/Microsoft.Maui.Controls.resources.dll": {"locale": "hi"}, "lib/net9.0-windows10.0.19041/hr/Microsoft.Maui.Controls.resources.dll": {"locale": "hr"}, "lib/net9.0-windows10.0.19041/hu/Microsoft.Maui.Controls.resources.dll": {"locale": "hu"}, "lib/net9.0-windows10.0.19041/id/Microsoft.Maui.Controls.resources.dll": {"locale": "id"}, "lib/net9.0-windows10.0.19041/it/Microsoft.Maui.Controls.resources.dll": {"locale": "it"}, "lib/net9.0-windows10.0.19041/ja/Microsoft.Maui.Controls.resources.dll": {"locale": "ja"}, "lib/net9.0-windows10.0.19041/ko/Microsoft.Maui.Controls.resources.dll": {"locale": "ko"}, "lib/net9.0-windows10.0.19041/ms/Microsoft.Maui.Controls.resources.dll": {"locale": "ms"}, "lib/net9.0-windows10.0.19041/nb/Microsoft.Maui.Controls.resources.dll": {"locale": "nb"}, "lib/net9.0-windows10.0.19041/nl/Microsoft.Maui.Controls.resources.dll": {"locale": "nl"}, "lib/net9.0-windows10.0.19041/pl/Microsoft.Maui.Controls.resources.dll": {"locale": "pl"}, "lib/net9.0-windows10.0.19041/pt-BR/Microsoft.Maui.Controls.resources.dll": {"locale": "pt-BR"}, "lib/net9.0-windows10.0.19041/pt/Microsoft.Maui.Controls.resources.dll": {"locale": "pt"}, "lib/net9.0-windows10.0.19041/ro/Microsoft.Maui.Controls.resources.dll": {"locale": "ro"}, "lib/net9.0-windows10.0.19041/ru/Microsoft.Maui.Controls.resources.dll": {"locale": "ru"}, "lib/net9.0-windows10.0.19041/sk/Microsoft.Maui.Controls.resources.dll": {"locale": "sk"}, "lib/net9.0-windows10.0.19041/sv/Microsoft.Maui.Controls.resources.dll": {"locale": "sv"}, "lib/net9.0-windows10.0.19041/th/Microsoft.Maui.Controls.resources.dll": {"locale": "th"}, "lib/net9.0-windows10.0.19041/tr/Microsoft.Maui.Controls.resources.dll": {"locale": "tr"}, "lib/net9.0-windows10.0.19041/uk/Microsoft.Maui.Controls.resources.dll": {"locale": "uk"}, "lib/net9.0-windows10.0.19041/vi/Microsoft.Maui.Controls.resources.dll": {"locale": "vi"}, "lib/net9.0-windows10.0.19041/zh-HK/Microsoft.Maui.Controls.resources.dll": {"locale": "zh-HK"}, "lib/net9.0-windows10.0.19041/zh-Hans/Microsoft.Maui.Controls.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0-windows10.0.19041/zh-Hant/Microsoft.Maui.Controls.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Maui.Controls.Xaml/9.0.10": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Maui.Controls.Core": "9.0.10", "Microsoft.Maui.Core": "9.0.10"}, "compile": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Xaml.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Xaml.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Maui.Core/9.0.10": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Graphics.Win2D": "1.2.0", "Microsoft.Maui.Essentials": "9.0.10", "Microsoft.Maui.Graphics": "9.0.10", "Microsoft.Maui.Graphics.Win2D.WinUI.Desktop": "9.0.10", "Microsoft.Web.WebView2": "1.0.2792.45", "Microsoft.Windows.SDK.BuildTools": "10.0.22621.756", "Microsoft.WindowsAppSDK": "1.6.240923002"}, "compile": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.dll": {"related": ".pdb;.pri;.xml"}}, "build": {"buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Core.props": {}, "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Core.targets": {}}}, "Microsoft.Maui.Essentials/9.0.10": {"type": "package", "dependencies": {"Microsoft.Maui.Graphics": "9.0.10"}, "compile": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Essentials.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Essentials.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Maui.Graphics/9.0.10": {"type": "package", "dependencies": {"Microsoft.Graphics.Win2D": "1.2.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.1"}, "compile": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Maui.Graphics.Skia/9.0.10": {"type": "package", "dependencies": {"Microsoft.Maui.Graphics": "9.0.10", "SkiaSharp": "2.88.8", "SkiaSharp.Views.WinUI": "2.88.8"}, "compile": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.Skia.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.Skia.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Maui.Graphics.Win2D.WinUI.Desktop/9.0.10": {"type": "package", "dependencies": {"Microsoft.Graphics.Win2D": "1.2.0", "Microsoft.Maui.Graphics": "9.0.10"}, "compile": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll": {"related": ".pdb"}}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll": {"related": ".pdb"}}}, "Microsoft.Maui.Resizetizer/9.0.10": {"type": "package", "build": {"buildTransitive/Microsoft.Maui.Resizetizer.props": {}, "buildTransitive/Microsoft.Maui.Resizetizer.targets": {}}}, "Microsoft.Web.WebView2/1.0.2792.45": {"type": "package", "native": {"runtimes/win-x64/native/WebView2Loader.dll": {}}, "build": {"buildTransitive/Microsoft.Web.WebView2.targets": {}}}, "Microsoft.Win32.SystemEvents/9.0.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Windows.SDK.BuildTools/10.0.22621.756": {"type": "package", "build": {"buildTransitive/Microsoft.Windows.SDK.BuildTools.props": {}, "buildTransitive/Microsoft.Windows.SDK.BuildTools.targets": {}}}, "Microsoft.WindowsAppSDK/1.6.240923002": {"type": "package", "dependencies": {"Microsoft.Web.WebView2": "1.0.2651.64", "Microsoft.Windows.SDK.BuildTools": "10.0.22621.756"}, "compile": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "runtime": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "native": {"runtimes/win-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.targets": {}}}, "SkiaSharp/2.88.8": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.8", "SkiaSharp.NativeAssets.macOS": "2.88.8"}, "compile": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.NativeAssets.macOS/2.88.8": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "native": {"runtimes/win-x64/native/libSkiaSharp.dll": {}}}, "SkiaSharp.Views.WinUI/2.88.8": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK": "1.1.5", "SkiaSharp": "2.88.8"}, "compile": {"lib/net6.0-windows10.0.19041.0/SkiaSharp.Views.Windows.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0-windows10.0.19041.0/SkiaSharp.Views.Windows.dll": {"related": ".pdb;.xml"}}}, "System.Drawing.Common/9.0.0": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "9.0.0"}, "compile": {"lib/net9.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}, "lib/net9.0/System.Private.Windows.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}, "lib/net9.0/System.Private.Windows.Core.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Text.Json/9.0.0": {"type": "package", "compile": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/System.Text.Json.targets": {}}}, "ZXing.Net/0.16.9": {"type": "package", "compile": {"lib/net7.0/zxing.dll": {"related": ".XML"}}, "runtime": {"lib/net7.0/zxing.dll": {"related": ".XML"}}}}}, "libraries": {"Microsoft.Extensions.Configuration/9.0.0": {"sha512": "YIMO9T3JL8MeEXgVozKt2v79hquo/EFtnY0vgxmLnUvk1Rei/halI7kOWZL2RBeV9FMGzgM9LZA8CVaNwFMaNA==", "type": "package", "path": "microsoft.extensions.configuration/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.9.0.0.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"sha512": "lqvd7W3FGKUO1+ZoUEMaZ5XDJeWvjpy2/M/ptCGz3tXLD4HWVaSzjufsAsjemasBEg+2SxXVtYVvGt5r2nKDlg==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.9.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"sha512": "MCPrg7v3QgNMr0vX4vzRXvkNGgLg8vKWX0nKCWUxu2uPyMsaRgiRc1tHBnbTcfJMhMKj2slE/j2M9oGkd25DNw==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"sha512": "+6f2qv2a3dLwd5w6JanPIPs47CxRbnk+ZocMJUhv9NxP88VlOcJYZs9jY+MYSjxvady08bUZn6qgiNh7DadGgg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/9.0.0": {"sha512": "crjWyORoug0kK7RSNJBTeSE6VX8IQgLf3nUpTB9m62bPXp/tzbnOsnbe8TXEG0AASNaKZddnpHKw7fET8E++Pg==", "type": "package", "path": "microsoft.extensions.logging/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/net9.0/Microsoft.Extensions.Logging.dll", "lib/net9.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.9.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"sha512": "g0UfujELzlLbHoVG8kPKVBaW470Ewi+jnptGS9KUi6jcb+k2StujtK3m26DFSGGwQ/+bVgZfsWqNzlP6YOejvw==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Debug/9.0.0": {"sha512": "4wGlHsrLhYjLw4sFkfRixu2w4DK7dv60OjbvgbLGhUJk0eUPxYHhnszZ/P18nnAkfrPryvtOJ3ZTVev0kpqM6A==", "type": "package", "path": "microsoft.extensions.logging.debug/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Debug.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Debug.targets", "lib/net462/Microsoft.Extensions.Logging.Debug.dll", "lib/net462/Microsoft.Extensions.Logging.Debug.xml", "lib/net8.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net8.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net9.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net9.0/Microsoft.Extensions.Logging.Debug.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.xml", "microsoft.extensions.logging.debug.9.0.0.nupkg.sha512", "microsoft.extensions.logging.debug.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/9.0.0": {"sha512": "y2146b3jrPI3Q0lokKXdKLpmXqakYbDIPDV6r3M8SqvSf45WwOTzkyfDpxnZXJsJQEpAsAqjUq5Pu8RCJMjubg==", "type": "package", "path": "microsoft.extensions.options/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.0.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.0": {"sha512": "N3qEBzmLMYiASUlKxxFIISP4AiwuPTHF5uCh+2CWSwwzAJiIYx0kBJsS30cp1nvhSySFAVi30jecD307jV+8Kg==", "type": "package", "path": "microsoft.extensions.primitives/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Graphics.Win2D/1.2.0": {"sha512": "7bAo8ObjCy/br0eW0nONRfVKehJu5aDe/KQekWeNXslwTOO2rhrIfWaVGepsXyVqmqwHoLJ31g1HsT7FLdBCoQ==", "type": "package", "path": "microsoft.graphics.win2d/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Win2d.githash.txt", "build/Win2D.common.targets", "build/native/Microsoft.Graphics.Win2D.targets", "build/net45/Microsoft.Graphics.Win2D.targets", "build/net6.0-windows10.0.19041.0/Microsoft.Graphics.Win2D.targets", "build/win10/Microsoft.Graphics.Win2D.targets", "icon.png", "include/Microsoft.Graphics.Canvas.native.h", "include/arm64/Microsoft.Graphics.Canvas.h", "include/x64/Microsoft.Graphics.Canvas.h", "include/x86/Microsoft.Graphics.Canvas.h", "lib/net45/Microsoft.Graphics.Canvas.winmd", "lib/net6.0-windows10.0.19041.0/Microsoft.Graphics.Canvas.Interop.dll", "lib/uap10.0/Microsoft.Graphics.Canvas.winmd", "microsoft.graphics.win2d.1.2.0.nupkg.sha512", "microsoft.graphics.win2d.nuspec", "runtimes/win-arm64/native/Microsoft.Graphics.Canvas.dll", "runtimes/win-x64/native/Microsoft.Graphics.Canvas.dll", "runtimes/win-x86/native/Microsoft.Graphics.Canvas.dll"]}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"sha512": "s/s20YTVY9r9TPfTrN5g8zPF1YhwxyqO6PxUkrYTGI2B+OGPe9AdajWZrLhFqXIvqIW23fnUE4+ztrUWNU1+9g==", "type": "package", "path": "microsoft.io.recyclablememorystream/3.0.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/net6.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.xml", "microsoft.io.recyclablememorystream.3.0.1.nupkg.sha512", "microsoft.io.recyclablememorystream.nuspec"]}, "Microsoft.Maui.Controls/9.0.10": {"sha512": "3P2OU0vcTZqVXz4u2Pvux6P6IB/dvY9tBGBzYVYG/+GPa06nN1naNkGuOed4rdlspKqmj9TmFp4O0d6GIX0ieA==", "type": "package", "path": "microsoft.maui.controls/9.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "microsoft.maui.controls.9.0.10.nupkg.sha512", "microsoft.maui.controls.nuspec"]}, "Microsoft.Maui.Controls.Build.Tasks/9.0.10": {"sha512": "EQpfTIjNuY/pfTG4+TbyEJAW63SoU0GuwdjY1E2sZIJdTxNFTD36nClmZJpVNWf4ySol8rPu2kfOznbDNhv35Q==", "type": "package", "path": "microsoft.maui.controls.build.tasks/9.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/Microsoft.Maui.Controls.Build.Tasks.props", "buildTransitive/Microsoft.Maui.Controls.Build.Tasks.targets", "buildTransitive/net6.0-ios10.0/Microsoft.Maui.Controls.Build.Tasks.props", "buildTransitive/net6.0-ios10.0/Microsoft.Maui.Controls.Build.Tasks.targets", "buildTransitive/net6.0-ios10.0/Microsoft.Maui.Controls.iOS.targets", "buildTransitive/net6.0-maccatalyst13.1/Microsoft.Maui.Controls.Build.Tasks.props", "buildTransitive/net6.0-maccatalyst13.1/Microsoft.Maui.Controls.Build.Tasks.targets", "buildTransitive/net6.0-maccatalyst13.1/Microsoft.Maui.Controls.MacCatalyst.targets", "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Controls.Build.Tasks.props", "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Controls.Build.Tasks.targets", "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Sdk.Windows.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.BindingSourceGen.dll", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.BindingSourceGen.pdb", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.After.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.Before.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.dll", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.pdb", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.props", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Common.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.DefaultItems.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Globs.props", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.SingleProject.Before.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.SingleProject.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.SourceGen.dll", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.SourceGen.pdb", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.targets", "buildTransitive/netstandard2.0/Mono.Cecil.Mdb.dll", "buildTransitive/netstandard2.0/Mono.Cecil.Mdb.pdb", "buildTransitive/netstandard2.0/Mono.Cecil.Pdb.dll", "buildTransitive/netstandard2.0/Mono.Cecil.Pdb.pdb", "buildTransitive/netstandard2.0/Mono.Cecil.Rocks.dll", "buildTransitive/netstandard2.0/Mono.Cecil.Rocks.pdb", "buildTransitive/netstandard2.0/Mono.Cecil.dll", "buildTransitive/netstandard2.0/Mono.Cecil.pdb", "buildTransitive/netstandard2.0/System.CodeDom.dll", "buildTransitive/netstandard2.0/ar/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/ca/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/cs/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/da/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/de/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/el/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/es/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/fi/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/fr/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/he/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/hi/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/hr/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/hu/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/id/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/it/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/ja/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/ko/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/maui-blazor.aotprofile", "buildTransitive/netstandard2.0/maui.aotprofile", "buildTransitive/netstandard2.0/ms/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/nb/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/nl/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/pl/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/proguard.cfg", "buildTransitive/netstandard2.0/pt-BR/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/pt/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/ro/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/ru/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/sk/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/sv/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/th/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/tr/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/uk/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/vi/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/zh-HK/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/zh-Hans/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/zh-Hant/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "microsoft.maui.controls.build.tasks.9.0.10.nupkg.sha512", "microsoft.maui.controls.build.tasks.nuspec"]}, "Microsoft.Maui.Controls.Compatibility/9.0.10": {"sha512": "WYJC+EAmRZaNQPNz3ajBXEa5Cb5i6+1QMi1uq7OQlO7UwY/hweQfgDJrBu0XlyT3wh9adelufNcUsIr2xqFXaA==", "type": "package", "path": "microsoft.maui.controls.compatibility/9.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0-android35.0/Microsoft.Maui.Controls.Compatibility.aar", "lib/net9.0-android35.0/Microsoft.Maui.Controls.Compatibility.dll", "lib/net9.0-android35.0/Microsoft.Maui.Controls.Compatibility.pdb", "lib/net9.0-android35.0/Microsoft.Maui.Controls.Compatibility.xml", "lib/net9.0-ios18.0/Microsoft.Maui.Controls.Compatibility.dll", "lib/net9.0-ios18.0/Microsoft.Maui.Controls.Compatibility.pdb", "lib/net9.0-ios18.0/Microsoft.Maui.Controls.Compatibility.xml", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Controls.Compatibility.dll", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Controls.Compatibility.pdb", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Controls.Compatibility.xml", "lib/net9.0-tizen7.0/Microsoft.Maui.Controls.Compatibility.dll", "lib/net9.0-tizen7.0/Microsoft.Maui.Controls.Compatibility.pdb", "lib/net9.0-tizen7.0/Microsoft.Maui.Controls.Compatibility.xml", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Compatibility.dll", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Compatibility.pdb", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Compatibility.pri", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Compatibility.xml", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Controls.Compatibility.dll", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Controls.Compatibility.pdb", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Controls.Compatibility.pri", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Controls.Compatibility.xml", "lib/net9.0/Microsoft.Maui.Controls.Compatibility.dll", "lib/net9.0/Microsoft.Maui.Controls.Compatibility.pdb", "lib/net9.0/Microsoft.Maui.Controls.Compatibility.xml", "lib/netstandard2.0/Microsoft.Maui.Controls.Compatibility.dll", "lib/netstandard2.0/Microsoft.Maui.Controls.Compatibility.pdb", "lib/netstandard2.0/Microsoft.Maui.Controls.Compatibility.xml", "lib/netstandard2.1/Microsoft.Maui.Controls.Compatibility.dll", "lib/netstandard2.1/Microsoft.Maui.Controls.Compatibility.pdb", "lib/netstandard2.1/Microsoft.Maui.Controls.Compatibility.xml", "microsoft.maui.controls.compatibility.9.0.10.nupkg.sha512", "microsoft.maui.controls.compatibility.nuspec"]}, "Microsoft.Maui.Controls.Core/9.0.10": {"sha512": "YBliIXb3Z0bAWjrldkIV9ku2o+ciejaypvQQyzebvA16A0wNmIYJY7uGdygVwSmpcPL73ZwwMCBPD4IqD1ogrg==", "type": "package", "path": "microsoft.maui.controls.core/9.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0-android35.0/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net9.0-android35.0/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net9.0-android35.0/Microsoft.Maui.Controls.aar", "lib/net9.0-android35.0/Microsoft.Maui.Controls.dll", "lib/net9.0-android35.0/Microsoft.Maui.Controls.pdb", "lib/net9.0-android35.0/Microsoft.Maui.Controls.xml", "lib/net9.0-android35.0/ar/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/ca/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/cs/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/da/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/de/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/el/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/es/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/fi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/fr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/he/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/hi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/hr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/hu/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/id/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/it/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/ja/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/ko/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/ms/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/nb/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/nl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/pl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/pt/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/ro/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/ru/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/sk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/sv/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/th/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/tr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/uk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/vi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net9.0-ios18.0/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net9.0-ios18.0/Microsoft.Maui.Controls.dll", "lib/net9.0-ios18.0/Microsoft.Maui.Controls.pdb", "lib/net9.0-ios18.0/Microsoft.Maui.Controls.xml", "lib/net9.0-ios18.0/ar/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/ca/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/cs/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/da/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/de/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/el/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/es/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/fi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/fr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/he/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/hi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/hr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/hu/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/id/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/it/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/ja/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/ko/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/ms/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/nb/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/nl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/pl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/pt/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/ro/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/ru/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/sk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/sv/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/th/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/tr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/uk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/vi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net9.0-maccatalyst18.0/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Controls.dll", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Controls.pdb", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Controls.xml", "lib/net9.0-maccatalyst18.0/ar/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/ca/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/cs/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/da/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/de/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/el/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/es/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/fi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/fr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/he/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/hi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/hr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/hu/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/id/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/it/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/ja/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/ko/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/ms/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/nb/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/nl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/pl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/pt/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/ro/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/ru/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/sk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/sv/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/th/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/tr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/uk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/vi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net9.0-tizen7.0/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net9.0-tizen7.0/Microsoft.Maui.Controls.dll", "lib/net9.0-tizen7.0/Microsoft.Maui.Controls.pdb", "lib/net9.0-tizen7.0/Microsoft.Maui.Controls.xml", "lib/net9.0-tizen7.0/ar/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/ca/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/cs/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/da/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/de/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/el/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/es/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/fi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/fr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/he/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/hi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/hr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/hu/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/id/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/it/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/ja/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/ko/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/ms/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/nb/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/nl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/pl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/pt/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/ro/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/ru/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/sk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/sv/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/th/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/tr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/uk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/vi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net9.0-windows10.0.19041/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.dll", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.pdb", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.pri", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.xml", "lib/net9.0-windows10.0.19041/ar/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/ca/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/cs/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/da/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/de/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/el/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/es/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/fi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/fr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/he/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/hi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/hr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/hu/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/id/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/it/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/ja/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/ko/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/ms/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/nb/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/nl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/pl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/pt/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/ro/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/ru/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/sk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/sv/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/th/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/tr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/uk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/vi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net9.0-windows10.0.20348/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Controls.dll", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Controls.pdb", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Controls.pri", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Controls.xml", "lib/net9.0-windows10.0.20348/ar/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/ca/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/cs/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/da/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/de/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/el/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/es/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/fi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/fr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/he/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/hi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/hr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/hu/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/id/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/it/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/ja/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/ko/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/ms/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/nb/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/nl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/pl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/pt/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/ro/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/ru/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/sk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/sv/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/th/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/tr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/uk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/vi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/zh-<PERSON>/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net9.0/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net9.0/Microsoft.Maui.Controls.dll", "lib/net9.0/Microsoft.Maui.Controls.pdb", "lib/net9.0/Microsoft.Maui.Controls.xml", "lib/net9.0/ar/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/ca/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/cs/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/da/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/de/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/el/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/es/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/fi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/fr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/he/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/hi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/hr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/hu/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/id/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/it/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/ja/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/ko/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/ms/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/nb/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/nl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/pl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/pt/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/ro/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/ru/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/sk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/sv/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/th/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/tr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/uk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/vi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/netstandard2.0/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/netstandard2.0/Microsoft.Maui.Controls.dll", "lib/netstandard2.0/Microsoft.Maui.Controls.pdb", "lib/netstandard2.0/Microsoft.Maui.Controls.xml", "lib/netstandard2.0/ar/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/ca/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/cs/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/da/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/de/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/el/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/es/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/fi/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/fr/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/he/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/hi/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/hr/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/hu/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/id/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/it/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/ja/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/ko/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/ms/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/nb/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/nl/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/pl/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/pt/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/ro/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/ru/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/sk/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/sv/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/th/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/tr/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/uk/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/vi/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/netstandard2.1/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/netstandard2.1/Microsoft.Maui.Controls.dll", "lib/netstandard2.1/Microsoft.Maui.Controls.pdb", "lib/netstandard2.1/Microsoft.Maui.Controls.xml", "lib/netstandard2.1/ar/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/ca/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/cs/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/da/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/de/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/el/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/es/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/fi/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/fr/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/he/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/hi/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/hr/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/hu/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/id/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/it/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/ja/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/ko/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/ms/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/nb/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/nl/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/pl/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/pt/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/ro/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/ru/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/sk/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/sv/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/th/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/tr/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/uk/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/vi/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/zh-<PERSON>/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/zh-Hant/Microsoft.Maui.Controls.resources.dll", "microsoft.maui.controls.core.9.0.10.nupkg.sha512", "microsoft.maui.controls.core.nuspec"]}, "Microsoft.Maui.Controls.Xaml/9.0.10": {"sha512": "5cgWqRdTAtzps/Qz2RB4XZ1x5usVHeSjFVHE8LV2WG1avEztSteGQGmvgDYjnJrGPnCNcp4S7Ivh6mxDJwHDQQ==", "type": "package", "path": "microsoft.maui.controls.xaml/9.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0-android35.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net9.0-android35.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net9.0-android35.0/Microsoft.Maui.Controls.Xaml.dll", "lib/net9.0-android35.0/Microsoft.Maui.Controls.Xaml.pdb", "lib/net9.0-android35.0/Microsoft.Maui.Controls.Xaml.xml", "lib/net9.0-ios18.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net9.0-ios18.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net9.0-ios18.0/Microsoft.Maui.Controls.Xaml.dll", "lib/net9.0-ios18.0/Microsoft.Maui.Controls.Xaml.pdb", "lib/net9.0-ios18.0/Microsoft.Maui.Controls.Xaml.xml", "lib/net9.0-maccatalyst18.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net9.0-maccatalyst18.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Controls.Xaml.dll", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Controls.Xaml.pdb", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Controls.Xaml.xml", "lib/net9.0-tizen7.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net9.0-tizen7.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net9.0-tizen7.0/Microsoft.Maui.Controls.Xaml.dll", "lib/net9.0-tizen7.0/Microsoft.Maui.Controls.Xaml.pdb", "lib/net9.0-tizen7.0/Microsoft.Maui.Controls.Xaml.xml", "lib/net9.0-windows10.0.19041/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net9.0-windows10.0.19041/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Xaml.dll", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Xaml.pdb", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Xaml.xml", "lib/net9.0-windows10.0.20348/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net9.0-windows10.0.20348/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Controls.Xaml.dll", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Controls.Xaml.pdb", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Controls.Xaml.xml", "lib/net9.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net9.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net9.0/Microsoft.Maui.Controls.Xaml.dll", "lib/net9.0/Microsoft.Maui.Controls.Xaml.pdb", "lib/net9.0/Microsoft.Maui.Controls.Xaml.xml", "lib/netstandard2.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/netstandard2.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/netstandard2.0/Microsoft.Maui.Controls.Xaml.dll", "lib/netstandard2.0/Microsoft.Maui.Controls.Xaml.pdb", "lib/netstandard2.0/Microsoft.Maui.Controls.Xaml.xml", "lib/netstandard2.1/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/netstandard2.1/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/netstandard2.1/Microsoft.Maui.Controls.Xaml.dll", "lib/netstandard2.1/Microsoft.Maui.Controls.Xaml.pdb", "lib/netstandard2.1/Microsoft.Maui.Controls.Xaml.xml", "microsoft.maui.controls.xaml.9.0.10.nupkg.sha512", "microsoft.maui.controls.xaml.nuspec"]}, "Microsoft.Maui.Core/9.0.10": {"sha512": "7hrCvNUiF5hPLB+IDcFJ9ncD1ciPNJkUO1NxFMoeDKEttZqt/j8+za/XaGxRgzZWF1QaIXaee+WI8jwx4/G5+g==", "type": "package", "path": "microsoft.maui.core/9.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/Microsoft.Maui.Core.After.targets", "buildTransitive/Microsoft.Maui.Core.Before.targets", "buildTransitive/Microsoft.Maui.Core.BundledVersions.targets", "buildTransitive/Microsoft.Maui.Core.props", "buildTransitive/Microsoft.Maui.Core.targets", "buildTransitive/WinUI.targets", "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Core.props", "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Core.targets", "lib/net9.0-android35.0/Microsoft.Maui.aar", "lib/net9.0-android35.0/Microsoft.Maui.dll", "lib/net9.0-android35.0/Microsoft.Maui.pdb", "lib/net9.0-android35.0/Microsoft.Maui.xml", "lib/net9.0-android35.0/maui.aar", "lib/net9.0-ios18.0/Microsoft.Maui.dll", "lib/net9.0-ios18.0/Microsoft.Maui.pdb", "lib/net9.0-ios18.0/Microsoft.Maui.xml", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.dll", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.pdb", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.xml", "lib/net9.0-tizen7.0/Microsoft.Maui.dll", "lib/net9.0-tizen7.0/Microsoft.Maui.pdb", "lib/net9.0-tizen7.0/Microsoft.Maui.xml", "lib/net9.0-windows10.0.19041/Microsoft.Maui.dll", "lib/net9.0-windows10.0.19041/Microsoft.Maui.pdb", "lib/net9.0-windows10.0.19041/Microsoft.Maui.pri", "lib/net9.0-windows10.0.19041/Microsoft.Maui.xml", "lib/net9.0-windows10.0.20348/Microsoft.Maui.dll", "lib/net9.0-windows10.0.20348/Microsoft.Maui.pdb", "lib/net9.0-windows10.0.20348/Microsoft.Maui.pri", "lib/net9.0-windows10.0.20348/Microsoft.Maui.xml", "lib/net9.0/Microsoft.Maui.dll", "lib/net9.0/Microsoft.Maui.pdb", "lib/net9.0/Microsoft.Maui.xml", "lib/netstandard2.0/Microsoft.Maui.dll", "lib/netstandard2.0/Microsoft.Maui.pdb", "lib/netstandard2.0/Microsoft.Maui.xml", "lib/netstandard2.1/Microsoft.Maui.dll", "lib/netstandard2.1/Microsoft.Maui.pdb", "lib/netstandard2.1/Microsoft.Maui.xml", "microsoft.maui.core.9.0.10.nupkg.sha512", "microsoft.maui.core.nuspec"]}, "Microsoft.Maui.Essentials/9.0.10": {"sha512": "F7bpJf7YP8Yxc3+oF9JzVZzEGuw3Pn30ULYomIyFKaUJvw0ceKGhv/QnXrKpwfAY/xAXTN7p0MVsRrEf5BsS1A==", "type": "package", "path": "microsoft.maui.essentials/9.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0-android35.0/Microsoft.Maui.Essentials.aar", "lib/net9.0-android35.0/Microsoft.Maui.Essentials.dll", "lib/net9.0-android35.0/Microsoft.Maui.Essentials.pdb", "lib/net9.0-android35.0/Microsoft.Maui.Essentials.xml", "lib/net9.0-ios18.0/Microsoft.Maui.Essentials.dll", "lib/net9.0-ios18.0/Microsoft.Maui.Essentials.pdb", "lib/net9.0-ios18.0/Microsoft.Maui.Essentials.xml", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Essentials.dll", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Essentials.pdb", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Essentials.xml", "lib/net9.0-tizen7.0/Microsoft.Maui.Essentials.dll", "lib/net9.0-tizen7.0/Microsoft.Maui.Essentials.pdb", "lib/net9.0-tizen7.0/Microsoft.Maui.Essentials.xml", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Essentials.dll", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Essentials.pdb", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Essentials.xml", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Essentials.dll", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Essentials.pdb", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Essentials.xml", "lib/net9.0/Microsoft.Maui.Essentials.dll", "lib/net9.0/Microsoft.Maui.Essentials.pdb", "lib/net9.0/Microsoft.Maui.Essentials.xml", "lib/netstandard2.0/Microsoft.Maui.Essentials.dll", "lib/netstandard2.0/Microsoft.Maui.Essentials.pdb", "lib/netstandard2.0/Microsoft.Maui.Essentials.xml", "lib/netstandard2.1/Microsoft.Maui.Essentials.dll", "lib/netstandard2.1/Microsoft.Maui.Essentials.pdb", "lib/netstandard2.1/Microsoft.Maui.Essentials.xml", "microsoft.maui.essentials.9.0.10.nupkg.sha512", "microsoft.maui.essentials.nuspec"]}, "Microsoft.Maui.Graphics/9.0.10": {"sha512": "QeM0B5H7OTqgY42sryVObbtvi55rWJPqFbBUZ6l344eaz07KsdUKon6WrcyJsuybVcLQcPV5HpLevFfk+lV0Yw==", "type": "package", "path": "microsoft.maui.graphics/9.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0-android35.0/Microsoft.Maui.Graphics.dll", "lib/net9.0-android35.0/Microsoft.Maui.Graphics.pdb", "lib/net9.0-android35.0/Microsoft.Maui.Graphics.xml", "lib/net9.0-ios18.0/Microsoft.Maui.Graphics.dll", "lib/net9.0-ios18.0/Microsoft.Maui.Graphics.pdb", "lib/net9.0-ios18.0/Microsoft.Maui.Graphics.xml", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Graphics.dll", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Graphics.pdb", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Graphics.xml", "lib/net9.0-macos15.0/Microsoft.Maui.Graphics.dll", "lib/net9.0-macos15.0/Microsoft.Maui.Graphics.pdb", "lib/net9.0-macos15.0/Microsoft.Maui.Graphics.xml", "lib/net9.0-tizen7.0/Microsoft.Maui.Graphics.dll", "lib/net9.0-tizen7.0/Microsoft.Maui.Graphics.pdb", "lib/net9.0-tizen7.0/Microsoft.Maui.Graphics.xml", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.dll", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.pdb", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.xml", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Graphics.dll", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Graphics.pdb", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Graphics.xml", "lib/net9.0/Microsoft.Maui.Graphics.dll", "lib/net9.0/Microsoft.Maui.Graphics.pdb", "lib/net9.0/Microsoft.Maui.Graphics.xml", "lib/netstandard2.0/Microsoft.Maui.Graphics.dll", "lib/netstandard2.0/Microsoft.Maui.Graphics.pdb", "lib/netstandard2.0/Microsoft.Maui.Graphics.xml", "lib/netstandard2.1/Microsoft.Maui.Graphics.dll", "lib/netstandard2.1/Microsoft.Maui.Graphics.pdb", "lib/netstandard2.1/Microsoft.Maui.Graphics.xml", "microsoft.maui.graphics.9.0.10.nupkg.sha512", "microsoft.maui.graphics.nuspec"]}, "Microsoft.Maui.Graphics.Skia/9.0.10": {"sha512": "ZDPUNEjz7fGkg2+XOXGjmoRG12FEqTIO6xIVWgBks25Dhs6d4hYaP9LvnS1uzne+YhKSPUCgFP3jCWLcCSiz2g==", "type": "package", "path": "microsoft.maui.graphics.skia/9.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0-android35.0/Microsoft.Maui.Graphics.Skia.dll", "lib/net9.0-android35.0/Microsoft.Maui.Graphics.Skia.pdb", "lib/net9.0-android35.0/Microsoft.Maui.Graphics.Skia.xml", "lib/net9.0-ios18.0/Microsoft.Maui.Graphics.Skia.dll", "lib/net9.0-ios18.0/Microsoft.Maui.Graphics.Skia.pdb", "lib/net9.0-ios18.0/Microsoft.Maui.Graphics.Skia.xml", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Graphics.Skia.dll", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Graphics.Skia.pdb", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Graphics.Skia.xml", "lib/net9.0-macos15.0/Microsoft.Maui.Graphics.Skia.dll", "lib/net9.0-macos15.0/Microsoft.Maui.Graphics.Skia.pdb", "lib/net9.0-macos15.0/Microsoft.Maui.Graphics.Skia.xml", "lib/net9.0-tizen7.0/Microsoft.Maui.Graphics.Skia.dll", "lib/net9.0-tizen7.0/Microsoft.Maui.Graphics.Skia.pdb", "lib/net9.0-tizen7.0/Microsoft.Maui.Graphics.Skia.xml", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.Skia.dll", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.Skia.pdb", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.Skia.xml", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Graphics.Skia.dll", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Graphics.Skia.pdb", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Graphics.Skia.xml", "lib/net9.0/Microsoft.Maui.Graphics.Skia.dll", "lib/net9.0/Microsoft.Maui.Graphics.Skia.pdb", "lib/net9.0/Microsoft.Maui.Graphics.Skia.xml", "lib/netstandard2.0/Microsoft.Maui.Graphics.Skia.dll", "lib/netstandard2.0/Microsoft.Maui.Graphics.Skia.pdb", "lib/netstandard2.0/Microsoft.Maui.Graphics.Skia.xml", "lib/netstandard2.1/Microsoft.Maui.Graphics.Skia.dll", "lib/netstandard2.1/Microsoft.Maui.Graphics.Skia.pdb", "lib/netstandard2.1/Microsoft.Maui.Graphics.Skia.xml", "microsoft.maui.graphics.skia.9.0.10.nupkg.sha512", "microsoft.maui.graphics.skia.nuspec"]}, "Microsoft.Maui.Graphics.Win2D.WinUI.Desktop/9.0.10": {"sha512": "u8afvuWnjY+vlQpk8xWVU79+++g1ONWJhC9ehMnSpr9GK827mqKqWdZZIXFFb9pRoo+KGgbZ5TNujjUO6pIPpg==", "type": "package", "path": "microsoft.maui.graphics.win2d.winui.desktop/9.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.pdb", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.pdb", "microsoft.maui.graphics.win2d.winui.desktop.9.0.10.nupkg.sha512", "microsoft.maui.graphics.win2d.winui.desktop.nuspec"]}, "Microsoft.Maui.Resizetizer/9.0.10": {"sha512": "cQLA4jeFF8mAVmqK6eJLSN374WK9+plVXpzxyVYd3Kes4d+JHRmjGrCrBDEkWlOXmH6jpAcGLlMa2utuuJDwOA==", "type": "package", "path": "microsoft.maui.resizetizer/9.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/ExCSS.dll", "buildTransitive/Fizzler.dll", "buildTransitive/HarfBuzzSharp.dll", "buildTransitive/HarfBuzzSharp.pdb", "buildTransitive/Microsoft.Bcl.AsyncInterfaces.dll", "buildTransitive/Microsoft.Maui.Resizetizer.After.targets", "buildTransitive/Microsoft.Maui.Resizetizer.Before.targets", "buildTransitive/Microsoft.Maui.Resizetizer.dll", "buildTransitive/Microsoft.Maui.Resizetizer.pdb", "buildTransitive/Microsoft.Maui.Resizetizer.props", "buildTransitive/Microsoft.Maui.Resizetizer.targets", "buildTransitive/ShimSkiaSharp.dll", "buildTransitive/SkiaSharp.HarfBuzz.dll", "buildTransitive/SkiaSharp.HarfBuzz.pdb", "buildTransitive/SkiaSharp.dll", "buildTransitive/SkiaSharp.pdb", "buildTransitive/Svg.Custom.dll", "buildTransitive/Svg.Model.dll", "buildTransitive/Svg.Skia.dll", "buildTransitive/System.Buffers.dll", "buildTransitive/System.IO.UnmanagedMemoryStream.dll", "buildTransitive/System.Memory.dll", "buildTransitive/System.Numerics.Vectors.dll", "buildTransitive/System.ObjectModel.dll", "buildTransitive/System.Runtime.CompilerServices.Unsafe.dll", "buildTransitive/arm/libHarfBuzzSharp.so", "buildTransitive/arm/libSkiaSharp.so", "buildTransitive/arm64/libHarfBuzzSharp.dll", "buildTransitive/arm64/libHarfBuzzSharp.so", "buildTransitive/arm64/libSkiaSharp.dll", "buildTransitive/arm64/libSkiaSharp.so", "buildTransitive/libHarfBuzzSharp.dylib", "buildTransitive/libSkiaSharp.dylib", "buildTransitive/musl-x64/libHarfBuzzSharp.so", "buildTransitive/musl-x64/libSkiaSharp.so", "buildTransitive/x64/libHarfBuzzSharp.dll", "buildTransitive/x64/libHarfBuzzSharp.so", "buildTransitive/x64/libSkiaSharp.dll", "buildTransitive/x64/libSkiaSharp.so", "buildTransitive/x86/libHarfBuzzSharp.dll", "buildTransitive/x86/libSkiaSharp.dll", "microsoft.maui.resizetizer.9.0.10.nupkg.sha512", "microsoft.maui.resizetizer.nuspec"]}, "Microsoft.Web.WebView2/1.0.2792.45": {"sha512": "KOlLJSq70OySfU8mdhWdh9iOyApazWsIb6CmSz+YTJ5MmwLcsCLMW0qemORo7Si3A7VhLDIH3jwpMhPxodfkuA==", "type": "package", "path": "microsoft.web.webview2/1.0.2792.45", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "NOTICE.txt", "WebView2.idl", "WebView2.tlb", "build/Common.targets", "build/Microsoft.Web.WebView2.targets", "build/WebView2Rules.Project.xml", "build/native/Microsoft.Web.WebView2.targets", "build/native/arm64/WebView2Loader.dll", "build/native/arm64/WebView2Loader.dll.lib", "build/native/arm64/WebView2LoaderStatic.lib", "build/native/include-winrt/WebView2Interop.h", "build/native/include-winrt/WebView2Interop.idl", "build/native/include-winrt/WebView2Interop.tlb", "build/native/include/WebView2.h", "build/native/include/WebView2EnvironmentOptions.h", "build/native/x64/WebView2Loader.dll", "build/native/x64/WebView2Loader.dll.lib", "build/native/x64/WebView2LoaderStatic.lib", "build/native/x86/WebView2Loader.dll", "build/native/x86/WebView2Loader.dll.lib", "build/native/x86/WebView2LoaderStatic.lib", "build/wv2winrt.targets", "buildTransitive/Microsoft.Web.WebView2.targets", "lib/Microsoft.Web.WebView2.Core.winmd", "lib/net462/Microsoft.Web.WebView2.Core.dll", "lib/net462/Microsoft.Web.WebView2.Core.xml", "lib/net462/Microsoft.Web.WebView2.WinForms.dll", "lib/net462/Microsoft.Web.WebView2.WinForms.xml", "lib/net462/Microsoft.Web.WebView2.Wpf.dll", "lib/net462/Microsoft.Web.WebView2.Wpf.xml", "lib_manual/net6.0-windows10.0.17763.0/Microsoft.Web.WebView2.Core.Projection.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Core.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Core.xml", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.xml", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.xml", "microsoft.web.webview2.1.0.2792.45.nupkg.sha512", "microsoft.web.webview2.nuspec", "runtimes/win-arm64/native/WebView2Loader.dll", "runtimes/win-arm64/native_uap/Microsoft.Web.WebView2.Core.dll", "runtimes/win-x64/native/WebView2Loader.dll", "runtimes/win-x64/native_uap/Microsoft.Web.WebView2.Core.dll", "runtimes/win-x86/native/WebView2Loader.dll", "runtimes/win-x86/native_uap/Microsoft.Web.WebView2.Core.dll", "tools/VisualStudioToolsManifest.xml", "tools/wv2winrt/Antlr3.Runtime.dll", "tools/wv2winrt/Antlr4.StringTemplate.dll", "tools/wv2winrt/System.Buffers.dll", "tools/wv2winrt/System.CommandLine.DragonFruit.dll", "tools/wv2winrt/System.CommandLine.Rendering.dll", "tools/wv2winrt/System.CommandLine.dll", "tools/wv2winrt/System.Memory.dll", "tools/wv2winrt/System.Numerics.Vectors.dll", "tools/wv2winrt/System.Runtime.CompilerServices.Unsafe.dll", "tools/wv2winrt/codegen_util.dll", "tools/wv2winrt/concrt140_app.dll", "tools/wv2winrt/cs/System.CommandLine.resources.dll", "tools/wv2winrt/de/System.CommandLine.resources.dll", "tools/wv2winrt/es/System.CommandLine.resources.dll", "tools/wv2winrt/fr/System.CommandLine.resources.dll", "tools/wv2winrt/it/System.CommandLine.resources.dll", "tools/wv2winrt/ja/System.CommandLine.resources.dll", "tools/wv2winrt/ko/System.CommandLine.resources.dll", "tools/wv2winrt/msvcp140_1_app.dll", "tools/wv2winrt/msvcp140_2_app.dll", "tools/wv2winrt/msvcp140_app.dll", "tools/wv2winrt/pl/System.CommandLine.resources.dll", "tools/wv2winrt/pt-BR/System.CommandLine.resources.dll", "tools/wv2winrt/ru/System.CommandLine.resources.dll", "tools/wv2winrt/tr/System.CommandLine.resources.dll", "tools/wv2winrt/type_hierarchy.dll", "tools/wv2winrt/vcamp140_app.dll", "tools/wv2winrt/vccorlib140_app.dll", "tools/wv2winrt/vcomp140_app.dll", "tools/wv2winrt/vcruntime140_app.dll", "tools/wv2winrt/winrt_winmd.dll", "tools/wv2winrt/winrt_winmd.winmd", "tools/wv2winrt/wv2winrt.exe", "tools/wv2winrt/wv2winrt.exe.config", "tools/wv2winrt/wv2winrt.xml", "tools/wv2winrt/zh-Hans/System.CommandLine.resources.dll", "tools/wv2winrt/zh-Hant/System.CommandLine.resources.dll"]}, "Microsoft.Win32.SystemEvents/9.0.0": {"sha512": "z8FfGIaoeALdD+KF44A2uP8PZIQQtDGiXsOLuN8nohbKhkyKt7zGaZb+fKiCxTuBqG22Q7myIAioSWaIcOOrOw==", "type": "package", "path": "microsoft.win32.systemevents/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.SystemEvents.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "lib/net462/Microsoft.Win32.SystemEvents.dll", "lib/net462/Microsoft.Win32.SystemEvents.xml", "lib/net8.0/Microsoft.Win32.SystemEvents.dll", "lib/net8.0/Microsoft.Win32.SystemEvents.xml", "lib/net9.0/Microsoft.Win32.SystemEvents.dll", "lib/net9.0/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.9.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "Microsoft.Windows.SDK.BuildTools/10.0.22621.756": {"sha512": "7ZL2sFSioYm1Ry067Kw1hg0SCcW5kuVezC2SwjGbcPE61Nn+gTbH86T73G3LcEOVj0S3IZzNuE/29gZvOLS7VA==", "type": "package", "path": "microsoft.windows.sdk.buildtools/10.0.22621.756", "files": [".nupkg.metadata", ".signature.p7s", "bin/10.0.22621.0/arm/AccChecker/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.22621.0/arm/DeployUtil.exe", "bin/10.0.22621.0/arm64/AccChecker/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.22621.0/arm64/ComparePackage.exe", "bin/10.0.22621.0/arm64/DeployUtil.exe", "bin/10.0.22621.0/arm64/MakeCert.exe", "bin/10.0.22621.0/arm64/Microsoft.ComparePackage.Lib.dll", "bin/10.0.22621.0/arm64/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.22621.0/arm64/Microsoft.PackageEditor.Lib.dll", "bin/10.0.22621.0/arm64/Microsoft.Tools.Connectivity.dll", "bin/10.0.22621.0/arm64/Microsoft.Tools.Deploy.dll", "bin/10.0.22621.0/arm64/Microsoft.Windows.Build.Appx.AppxPackaging.dll.manifest", "bin/10.0.22621.0/arm64/Microsoft.Windows.Build.Appx.AppxSip.dll.manifest", "bin/10.0.22621.0/arm64/Microsoft.Windows.Build.Appx.OpcServices.dll.manifest", "bin/10.0.22621.0/arm64/Microsoft.Windows.Build.Signing.mssign32.dll.manifest", "bin/10.0.22621.0/arm64/Microsoft.Windows.Build.Signing.wintrust.dll.manifest", "bin/10.0.22621.0/arm64/PackageEditor.exe", "bin/10.0.22621.0/arm64/ServicingCommon.dll", "bin/10.0.22621.0/arm64/SirepClient.assembly.manifest", "bin/10.0.22621.0/arm64/SirepClient.dll", "bin/10.0.22621.0/arm64/SirepInterop.dll", "bin/10.0.22621.0/arm64/SshClient.dll", "bin/10.0.22621.0/arm64/WinAppDeployCmd.exe", "bin/10.0.22621.0/arm64/WinAppDeployCommon.dll", "bin/10.0.22621.0/arm64/appxpackaging.dll", "bin/10.0.22621.0/arm64/appxsip.dll", "bin/10.0.22621.0/arm64/en-US/AppxPackaging.dll.mui", "bin/10.0.22621.0/arm64/en/Microsoft.Tools.Deploy.resources.dll", "bin/10.0.22621.0/arm64/en/WinAppDeployCmd.resources.dll", "bin/10.0.22621.0/arm64/ipoverusb.discoverpartners.dll", "bin/10.0.22621.0/arm64/makeappx.exe", "bin/10.0.22621.0/arm64/makecat.exe", "bin/10.0.22621.0/arm64/makecat.exe.manifest", "bin/10.0.22621.0/arm64/makepri.exe", "bin/10.0.22621.0/arm64/mc.exe", "bin/10.0.22621.0/arm64/mdmerge.exe", "bin/10.0.22621.0/arm64/midl.exe", "bin/10.0.22621.0/arm64/midlc.exe", "bin/10.0.22621.0/arm64/midlrt.exe", "bin/10.0.22621.0/arm64/midlrtmd.dll", "bin/10.0.22621.0/arm64/mrmsupport.dll", "bin/10.0.22621.0/arm64/msisip.dll", "bin/10.0.22621.0/arm64/mssign32.dll", "bin/10.0.22621.0/arm64/mt.exe", "bin/10.0.22621.0/arm64/mt.exe.config", "bin/10.0.22621.0/arm64/opcservices.dll", "bin/10.0.22621.0/arm64/rc.exe", "bin/10.0.22621.0/arm64/rcdll.dll", "bin/10.0.22621.0/arm64/signtool.exe", "bin/10.0.22621.0/arm64/signtool.exe.manifest", "bin/10.0.22621.0/arm64/tracewpp.exe", "bin/10.0.22621.0/arm64/uuidgen.exe", "bin/10.0.22621.0/arm64/winmdidl.exe", "bin/10.0.22621.0/arm64/wintrust.dll", "bin/10.0.22621.0/arm64/wintrust.dll.ini", "bin/10.0.22621.0/x64/AccChecker/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.22621.0/x64/ComparePackage.exe", "bin/10.0.22621.0/x64/DeployUtil.exe", "bin/10.0.22621.0/x64/MakeCert.exe", "bin/10.0.22621.0/x64/Microsoft.ComparePackage.Lib.dll", "bin/10.0.22621.0/x64/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.22621.0/x64/Microsoft.PackageEditor.Lib.dll", "bin/10.0.22621.0/x64/Microsoft.Tools.Connectivity.dll", "bin/10.0.22621.0/x64/Microsoft.Tools.Deploy.dll", "bin/10.0.22621.0/x64/Microsoft.Windows.Build.Appx.AppxPackaging.dll.manifest", "bin/10.0.22621.0/x64/Microsoft.Windows.Build.Appx.AppxSip.dll.manifest", "bin/10.0.22621.0/x64/Microsoft.Windows.Build.Appx.OpcServices.dll.manifest", "bin/10.0.22621.0/x64/Microsoft.Windows.Build.Signing.mssign32.dll.manifest", "bin/10.0.22621.0/x64/Microsoft.Windows.Build.Signing.wintrust.dll.manifest", "bin/10.0.22621.0/x64/PackageEditor.exe", "bin/10.0.22621.0/x64/ServicingCommon.dll", "bin/10.0.22621.0/x64/SirepClient.assembly.manifest", "bin/10.0.22621.0/x64/SirepClient.dll", "bin/10.0.22621.0/x64/SirepInterop.dll", "bin/10.0.22621.0/x64/SshClient.dll", "bin/10.0.22621.0/x64/WinAppDeployCmd.exe", "bin/10.0.22621.0/x64/WinAppDeployCommon.dll", "bin/10.0.22621.0/x64/appxpackaging.dll", "bin/10.0.22621.0/x64/appxsip.dll", "bin/10.0.22621.0/x64/en-US/AppxPackaging.dll.mui", "bin/10.0.22621.0/x64/en/Microsoft.Tools.Deploy.resources.dll", "bin/10.0.22621.0/x64/en/WinAppDeployCmd.resources.dll", "bin/10.0.22621.0/x64/ipoverusb.discoverpartners.dll", "bin/10.0.22621.0/x64/makeappx.exe", "bin/10.0.22621.0/x64/makecat.exe", "bin/10.0.22621.0/x64/makecat.exe.manifest", "bin/10.0.22621.0/x64/makepri.exe", "bin/10.0.22621.0/x64/mc.exe", "bin/10.0.22621.0/x64/mdmerge.exe", "bin/10.0.22621.0/x64/midl.exe", "bin/10.0.22621.0/x64/midlc.exe", "bin/10.0.22621.0/x64/midlrt.exe", "bin/10.0.22621.0/x64/midlrtmd.dll", "bin/10.0.22621.0/x64/mrmsupport.dll", "bin/10.0.22621.0/x64/msisip.dll", "bin/10.0.22621.0/x64/mssign32.dll", "bin/10.0.22621.0/x64/mt.exe", "bin/10.0.22621.0/x64/mt.exe.config", "bin/10.0.22621.0/x64/opcservices.dll", "bin/10.0.22621.0/x64/rc.exe", "bin/10.0.22621.0/x64/rcdll.dll", "bin/10.0.22621.0/x64/signtool.exe", "bin/10.0.22621.0/x64/signtool.exe.manifest", "bin/10.0.22621.0/x64/tracewpp.exe", "bin/10.0.22621.0/x64/uuidgen.exe", "bin/10.0.22621.0/x64/winmdidl.exe", "bin/10.0.22621.0/x64/wintrust.dll", "bin/10.0.22621.0/x64/wintrust.dll.ini", "bin/10.0.22621.0/x86/AccChecker/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.22621.0/x86/ComparePackage.exe", "bin/10.0.22621.0/x86/DeployUtil.exe", "bin/10.0.22621.0/x86/MakeCert.exe", "bin/10.0.22621.0/x86/Microsoft.ComparePackage.Lib.dll", "bin/10.0.22621.0/x86/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.22621.0/x86/Microsoft.PackageEditor.Lib.dll", "bin/10.0.22621.0/x86/Microsoft.Tools.Connectivity.dll", "bin/10.0.22621.0/x86/Microsoft.Tools.Deploy.dll", "bin/10.0.22621.0/x86/Microsoft.Windows.Build.Appx.AppxPackaging.dll.manifest", "bin/10.0.22621.0/x86/Microsoft.Windows.Build.Appx.AppxSip.dll.manifest", "bin/10.0.22621.0/x86/Microsoft.Windows.Build.Appx.OpcServices.dll.manifest", "bin/10.0.22621.0/x86/Microsoft.Windows.Build.Signing.mssign32.dll.manifest", "bin/10.0.22621.0/x86/Microsoft.Windows.Build.Signing.wintrust.dll.manifest", "bin/10.0.22621.0/x86/PackageEditor.exe", "bin/10.0.22621.0/x86/ServicingCommon.dll", "bin/10.0.22621.0/x86/SirepClient.assembly.manifest", "bin/10.0.22621.0/x86/SirepClient.dll", "bin/10.0.22621.0/x86/SirepInterop.dll", "bin/10.0.22621.0/x86/SshClient.dll", "bin/10.0.22621.0/x86/WinAppDeployCmd.exe", "bin/10.0.22621.0/x86/WinAppDeployCommon.dll", "bin/10.0.22621.0/x86/appxpackaging.dll", "bin/10.0.22621.0/x86/appxsip.dll", "bin/10.0.22621.0/x86/en-US/AppxPackaging.dll.mui", "bin/10.0.22621.0/x86/en/Microsoft.Tools.Deploy.resources.dll", "bin/10.0.22621.0/x86/en/WinAppDeployCmd.resources.dll", "bin/10.0.22621.0/x86/ipoverusb.discoverpartners.dll", "bin/10.0.22621.0/x86/makeappx.exe", "bin/10.0.22621.0/x86/makecat.exe", "bin/10.0.22621.0/x86/makecat.exe.manifest", "bin/10.0.22621.0/x86/makepri.exe", "bin/10.0.22621.0/x86/mc.exe", "bin/10.0.22621.0/x86/mdmerge.exe", "bin/10.0.22621.0/x86/midl.exe", "bin/10.0.22621.0/x86/midlc.exe", "bin/10.0.22621.0/x86/midlrt.exe", "bin/10.0.22621.0/x86/midlrtmd.dll", "bin/10.0.22621.0/x86/mrmsupport.dll", "bin/10.0.22621.0/x86/msisip.dll", "bin/10.0.22621.0/x86/mssign32.dll", "bin/10.0.22621.0/x86/mt.exe", "bin/10.0.22621.0/x86/mt.exe.config", "bin/10.0.22621.0/x86/opcservices.dll", "bin/10.0.22621.0/x86/rc.exe", "bin/10.0.22621.0/x86/rcdll.dll", "bin/10.0.22621.0/x86/signtool.exe", "bin/10.0.22621.0/x86/signtool.exe.manifest", "bin/10.0.22621.0/x86/tracewpp.exe", "bin/10.0.22621.0/x86/uuidgen.exe", "bin/10.0.22621.0/x86/winmdidl.exe", "bin/10.0.22621.0/x86/wintrust.dll", "bin/10.0.22621.0/x86/wintrust.dll.ini", "build/Microsoft.Windows.SDK.BuildTools.props", "build/Microsoft.Windows.SDK.BuildTools.targets", "buildTransitive/Microsoft.Windows.SDK.BuildTools.props", "buildTransitive/Microsoft.Windows.SDK.BuildTools.targets", "microsoft.windows.sdk.buildtools.10.0.22621.756.nupkg.sha512", "microsoft.windows.sdk.buildtools.nuspec", "schemas/10.0.22621.0/winrt/AppxManifestSchema.xsd", "schemas/10.0.22621.0/winrt/AppxManifestSchema2010_v2.xsd", "schemas/10.0.22621.0/winrt/AppxManifestSchema2013.xsd", "schemas/10.0.22621.0/winrt/FoundationManifestSchema.xsd", "schemas/10.0.22621.0/winrt/FoundationManifestSchema_v2.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v10.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v11.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v12.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v13.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v2.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v3.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v4.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v5.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v6.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v7.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v8.xsd"]}, "Microsoft.WindowsAppSDK/1.6.240923002": {"sha512": "7PfOz2scXU+AAM/GYge+f6s7k3DVI+R1P8MNPZQr56GOPCGw+csvcg3S5KZg47z/o04kNvWH3GKtWT1ML9tpZw==", "type": "package", "path": "microsoft.windowsappsdk/1.6.240923002", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "NOTICE.txt", "WindowsAppSDK-VersionInfo.json", "WindowsAppSDK-VersionInfo.xml", "build/AppDevPackageScripts/Add-AppDevPackage.ps1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/cs-CZ/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/de-DE/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/en-US/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/es-ES/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/fr-FR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/it-IT/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/ja-JP/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/ko-KR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/pl-PL/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/pt-BR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/ru-RU/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/tr-TR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/zh-CN/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/zh-TW/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Install.ps1", "build/AppDevPackageScripts/LogSideloadingTelemetry.ps1", "build/Landing/extras/br.png", "build/Landing/extras/br_snippet.png", "build/Landing/image.png", "build/Landing/index.template.html", "build/Landing/logo.png", "build/Landing/style.css", "build/Microsoft.Build.Msix.Common.props", "build/Microsoft.Build.Msix.Cpp.props", "build/Microsoft.Build.Msix.Cpp.targets", "build/Microsoft.Build.Msix.Cs.targets", "build/Microsoft.Build.Msix.DesignTime.targets", "build/Microsoft.Build.Msix.Packaging.targets", "build/Microsoft.Build.Msix.Pri.targets", "build/Microsoft.Build.Msix.props", "build/Microsoft.Build.Msix.targets", "build/Microsoft.InteractiveExperiences.Capabilities.props", "build/Microsoft.InteractiveExperiences.Capabilities.targets", "build/Microsoft.InteractiveExperiences.Common.props", "build/Microsoft.InteractiveExperiences.Common.targets", "build/Microsoft.InteractiveExperiences.props", "build/Microsoft.InteractiveExperiences.targets", "build/Microsoft.UI.Xaml.Markup.Compiler.BeforeCommon.targets", "build/Microsoft.UI.Xaml.Markup.Compiler.interop.targets", "build/Microsoft.UI.Xaml.Markup.Compiler.props", "build/Microsoft.UI.Xaml.Markup.Compiler.targets", "build/Microsoft.WinUI.AppX.targets", "build/Microsoft.WinUI.NET.Markup.Compiler.targets", "build/Microsoft.WinUI.ProjectCapabilities.props", "build/Microsoft.WinUI.References.targets", "build/Microsoft.WinUI.props", "build/Microsoft.WinUI.targets", "build/Microsoft.WindowsAppSDK.AppXReference.props", "build/Microsoft.WindowsAppSDK.Bootstrap.CS.targets", "build/Microsoft.WindowsAppSDK.BootstrapCommon.targets", "build/Microsoft.WindowsAppSDK.Common.props", "build/Microsoft.WindowsAppSDK.DWrite.ProjectCapabilities.props", "build/Microsoft.WindowsAppSDK.DWrite.props", "build/Microsoft.WindowsAppSDK.DWrite.targets", "build/Microsoft.WindowsAppSDK.DeploymentManager.CS.targets", "build/Microsoft.WindowsAppSDK.DeploymentManagerCommon.targets", "build/Microsoft.WindowsAppSDK.Foundation.props", "build/Microsoft.WindowsAppSDK.Foundation.targets", "build/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "build/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "build/Microsoft.WindowsAppSDK.Metapackage.props", "build/Microsoft.WindowsAppSDK.SelfContained.targets", "build/Microsoft.WindowsAppSDK.SingleFile.targets", "build/Microsoft.WindowsAppSDK.UndockedRegFreeWinRT.CS.targets", "build/Microsoft.WindowsAppSDK.UndockedRegFreeWinRTCommon.targets", "build/Microsoft.WindowsAppSDK.Widgets.targets", "build/Microsoft.WindowsAppSDK.WinUI.props", "build/Microsoft.WindowsAppSDK.WinUI.targets", "build/Microsoft.WindowsAppSDK.props", "build/Microsoft.WindowsAppSDK.targets", "build/Microsoft.Xaml.Tooling.targets", "build/MicrosoftWindowsAppSDKFoundationAppXVersion.props", "build/MrtCore.PriGen.targets", "build/MrtCore.References.targets", "build/MrtCore.props", "build/MrtCore.targets", "build/ProjectItemsSchema.xaml", "build/README.md", "build/Rules/MsixPackageDebugPropertyPage.xaml", "build/Rules/WindowsPackageTypePropertyPage.xaml", "build/Templates/Package.appinstaller", "build/native/LiftedWinRTClassRegistrations.xml", "build/native/Microsoft.InteractiveExperiences.props", "build/native/Microsoft.InteractiveExperiences.targets", "build/native/Microsoft.WinUI.References.targets", "build/native/Microsoft.WinUI.props", "build/native/Microsoft.WinUI.targets", "build/native/Microsoft.WindowsAppSDK.Foundation.props", "build/native/Microsoft.WindowsAppSDK.Foundation.targets", "build/native/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "build/native/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "build/native/Microsoft.WindowsAppSDK.Widgets.targets", "build/native/Microsoft.WindowsAppSDK.WinUI.props", "build/native/Microsoft.WindowsAppSDK.WinUI.targets", "build/native/Microsoft.WindowsAppSDK.props", "build/native/Microsoft.WindowsAppSDK.targets", "build/native/MrtCore.C.props", "build/native/MrtCore.props", "build/native/MrtCore.targets", "build/native/WindowsAppSDK-Nuget-Native.Bootstrap.targets", "build/native/WindowsAppSDK-Nuget-Native.C.props", "build/native/WindowsAppSDK-Nuget-Native.DeploymentManager.targets", "build/native/WindowsAppSDK-Nuget-Native.UndockedRegFreeWinRT.targets", "build/native/WindowsAppSDK-Nuget-Native.WinRt.props", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.ps1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/cs-CZ/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/de-DE/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/en-US/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/es-ES/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/fr-FR/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/it-IT/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/ja-JP/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/ko-KR/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/pl-PL/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/pt-BR/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/ru-RU/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/tr-TR/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/zh-CN/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/zh-TW/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Install.ps1", "buildTransitive/AppDevPackageScripts/LogSideloadingTelemetry.ps1", "buildTransitive/Landing/extras/br.png", "buildTransitive/Landing/extras/br_snippet.png", "buildTransitive/Landing/image.png", "buildTransitive/Landing/index.template.html", "buildTransitive/Landing/logo.png", "buildTransitive/Landing/style.css", "buildTransitive/Microsoft.Build.Msix.Common.props", "buildTransitive/Microsoft.Build.Msix.Cpp.props", "buildTransitive/Microsoft.Build.Msix.Cpp.targets", "buildTransitive/Microsoft.Build.Msix.Cs.targets", "buildTransitive/Microsoft.Build.Msix.DesignTime.targets", "buildTransitive/Microsoft.Build.Msix.Packaging.targets", "buildTransitive/Microsoft.Build.Msix.Pri.targets", "buildTransitive/Microsoft.Build.Msix.props", "buildTransitive/Microsoft.Build.Msix.targets", "buildTransitive/Microsoft.InteractiveExperiences.Capabilities.props", "buildTransitive/Microsoft.InteractiveExperiences.Capabilities.targets", "buildTransitive/Microsoft.InteractiveExperiences.Common.props", "buildTransitive/Microsoft.InteractiveExperiences.Common.targets", "buildTransitive/Microsoft.InteractiveExperiences.props", "buildTransitive/Microsoft.InteractiveExperiences.targets", "buildTransitive/Microsoft.UI.Xaml.Markup.Compiler.BeforeCommon.targets", "buildTransitive/Microsoft.UI.Xaml.Markup.Compiler.interop.targets", "buildTransitive/Microsoft.UI.Xaml.Markup.Compiler.props", "buildTransitive/Microsoft.UI.Xaml.Markup.Compiler.targets", "buildTransitive/Microsoft.WinUI.AppX.targets", "buildTransitive/Microsoft.WinUI.NET.Markup.Compiler.targets", "buildTransitive/Microsoft.WinUI.ProjectCapabilities.props", "buildTransitive/Microsoft.WinUI.References.targets", "buildTransitive/Microsoft.WinUI.props", "buildTransitive/Microsoft.WinUI.targets", "buildTransitive/Microsoft.WindowsAppSDK.AppXReference.props", "buildTransitive/Microsoft.WindowsAppSDK.Bootstrap.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.BootstrapCommon.targets", "buildTransitive/Microsoft.WindowsAppSDK.Common.props", "buildTransitive/Microsoft.WindowsAppSDK.DWrite.ProjectCapabilities.props", "buildTransitive/Microsoft.WindowsAppSDK.DWrite.props", "buildTransitive/Microsoft.WindowsAppSDK.DWrite.targets", "buildTransitive/Microsoft.WindowsAppSDK.DeploymentManager.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.DeploymentManagerCommon.targets", "buildTransitive/Microsoft.WindowsAppSDK.Foundation.props", "buildTransitive/Microsoft.WindowsAppSDK.Foundation.targets", "buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "buildTransitive/Microsoft.WindowsAppSDK.Metapackage.props", "buildTransitive/Microsoft.WindowsAppSDK.SelfContained.targets", "buildTransitive/Microsoft.WindowsAppSDK.SingleFile.targets", "buildTransitive/Microsoft.WindowsAppSDK.UndockedRegFreeWinRT.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.UndockedRegFreeWinRTCommon.targets", "buildTransitive/Microsoft.WindowsAppSDK.Widgets.targets", "buildTransitive/Microsoft.WindowsAppSDK.WinUI.props", "buildTransitive/Microsoft.WindowsAppSDK.WinUI.targets", "buildTransitive/Microsoft.WindowsAppSDK.props", "buildTransitive/Microsoft.WindowsAppSDK.targets", "buildTransitive/Microsoft.Xaml.Tooling.targets", "buildTransitive/MicrosoftWindowsAppSDKFoundationAppXVersion.props", "buildTransitive/MrtCore.PriGen.targets", "buildTransitive/MrtCore.References.targets", "buildTransitive/MrtCore.props", "buildTransitive/MrtCore.targets", "buildTransitive/ProjectItemsSchema.xaml", "buildTransitive/README.md", "buildTransitive/Rules/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/WindowsPackageTypePropertyPage.xaml", "buildTransitive/Templates/Package.appinstaller", "buildTransitive/native/LiftedWinRTClassRegistrations.xml", "buildTransitive/native/Microsoft.InteractiveExperiences.props", "buildTransitive/native/Microsoft.InteractiveExperiences.targets", "buildTransitive/native/Microsoft.WinUI.References.targets", "buildTransitive/native/Microsoft.WinUI.props", "buildTransitive/native/Microsoft.WinUI.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.Foundation.props", "buildTransitive/native/Microsoft.WindowsAppSDK.Foundation.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "buildTransitive/native/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.Widgets.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.WinUI.props", "buildTransitive/native/Microsoft.WindowsAppSDK.WinUI.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.props", "buildTransitive/native/Microsoft.WindowsAppSDK.targets", "buildTransitive/native/MrtCore.C.props", "buildTransitive/native/MrtCore.props", "buildTransitive/native/MrtCore.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.Bootstrap.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.C.props", "buildTransitive/native/WindowsAppSDK-Nuget-Native.DeploymentManager.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.UndockedRegFreeWinRT.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.WinRt.props", "include/DeploymentManagerAutoInitializer.cpp", "include/DeploymentManagerAutoInitializer.cs", "include/MRM.h", "include/MddBootstrap.h", "include/MddBootstrapAutoInitializer.cpp", "include/MddBootstrapAutoInitializer.cs", "include/Microsoft.UI.Composition.Interop.h", "include/Microsoft.UI.Dispatching.Interop.h", "include/Microsoft.UI.Input.InputCursor.Interop.h", "include/Microsoft.UI.Input.InputPreTranslateSource.Interop.h", "include/Microsoft.UI.Interop.h", "include/Microsoft.Windows.ApplicationModel.Resources.idl", "include/MsixDynamicDependency.h", "include/Security.AccessControl.h", "include/UndockedRegFreeWinRT-AutoInitializer.cpp", "include/UndockedRegFreeWinRT-AutoInitializer.cs", "include/WindowsAppRuntimeInsights.h", "include/WindowsAppSDK-VersionInfo.cs", "include/WindowsAppSDK-VersionInfo.h", "include/dwrite.h", "include/dwrite_1.h", "include/dwrite_2.h", "include/dwrite_3.h", "include/dwrite_core.h", "include/microsoft.ui.xaml.hosting.referencetracker.h", "include/microsoft.ui.xaml.hosting.referencetracker.idl", "include/microsoft.ui.xaml.media.dxinterop.h", "include/microsoft.ui.xaml.media.dxinterop.idl", "include/microsoft.ui.xaml.window.h", "include/microsoft.ui.xaml.window.idl", "include/wil_msixdynamicdependency.h", "include/winrt/Microsoft.UI.Composition.Interop.h", "include/winrt/Microsoft.UI.Input.InputCursor.Interop.h", "include/winrt/Microsoft.UI.Input.InputPreTranslateSource.Interop.h", "include/winrt/Microsoft.UI.Interop.h", "include/winrtdirect3d11.h", "include/winrtdirectxcommon.h", "include/xamlom.winui.h", "include/xamlom.winui.idl", "lib/native/win10-arm64/Microsoft.UI.Dispatching.lib", "lib/native/win10-x64/Microsoft.UI.Dispatching.lib", "lib/native/win10-x86/Microsoft.UI.Dispatching.lib", "lib/net6.0-windows10.0.17763.0/Microsoft.InteractiveExperiences.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.InteractiveExperiences.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.WinUI/Themes/generic.xaml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI/Themes/generic.xaml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppLifecycle.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppLifecycle.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Builder.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.Resources.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Management.Deployment.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Management.Deployment.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.PushNotifications.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.PushNotifications.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Security.AccessControl.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Security.AccessControl.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Storage.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Storage.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Power.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Power.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Widgets.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Widgets.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll", "lib/uap10.0.17763/Microsoft.Foundation.winmd", "lib/uap10.0.17763/Microsoft.Graphics.winmd", "lib/uap10.0.17763/Microsoft.Graphics.xml", "lib/uap10.0.17763/Microsoft.UI.winmd", "lib/uap10.0.17763/Microsoft.UI.xml", "lib/uap10.0.18362/Microsoft.Foundation.winmd", "lib/uap10.0.18362/Microsoft.Graphics.winmd", "lib/uap10.0.18362/Microsoft.Graphics.xml", "lib/uap10.0.18362/Microsoft.UI.winmd", "lib/uap10.0.18362/Microsoft.UI.xml", "lib/uap10.0/Microsoft.UI.Text.winmd", "lib/uap10.0/Microsoft.UI.Text.xml", "lib/uap10.0/Microsoft.UI.Xaml.winmd", "lib/uap10.0/Microsoft.UI.Xaml.xml", "lib/uap10.0/Microsoft.UI/Themes/generic.xaml", "lib/uap10.0/Microsoft.Windows.AppLifecycle.winmd", "lib/uap10.0/Microsoft.Windows.AppLifecycle.xml", "lib/uap10.0/Microsoft.Windows.AppNotifications.Builder.winmd", "lib/uap10.0/Microsoft.Windows.AppNotifications.Builder.xml", "lib/uap10.0/Microsoft.Windows.AppNotifications.winmd", "lib/uap10.0/Microsoft.Windows.AppNotifications.xml", "lib/uap10.0/Microsoft.Windows.ApplicationModel.DynamicDependency.winmd", "lib/uap10.0/Microsoft.Windows.ApplicationModel.DynamicDependency.xml", "lib/uap10.0/Microsoft.Windows.ApplicationModel.Resources.winmd", "lib/uap10.0/Microsoft.Windows.ApplicationModel.Resources.xml", "lib/uap10.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.winmd", "lib/uap10.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.xml", "lib/uap10.0/Microsoft.Windows.Globalization.winmd", "lib/uap10.0/Microsoft.Windows.Globalization.xml", "lib/uap10.0/Microsoft.Windows.Management.Deployment.winmd", "lib/uap10.0/Microsoft.Windows.Management.Deployment.xml", "lib/uap10.0/Microsoft.Windows.PushNotifications.winmd", "lib/uap10.0/Microsoft.Windows.PushNotifications.xml", "lib/uap10.0/Microsoft.Windows.Security.AccessControl.winmd", "lib/uap10.0/Microsoft.Windows.Security.AccessControl.xml", "lib/uap10.0/Microsoft.Windows.Storage.winmd", "lib/uap10.0/Microsoft.Windows.Storage.xml", "lib/uap10.0/Microsoft.Windows.System.Power.winmd", "lib/uap10.0/Microsoft.Windows.System.Power.xml", "lib/uap10.0/Microsoft.Windows.System.winmd", "lib/uap10.0/Microsoft.Windows.System.xml", "lib/uap10.0/Microsoft.Windows.Widgets.winmd", "lib/uap10.0/Microsoft.Windows.Widgets.xml", "lib/win10-arm64/DWriteCore.lib", "lib/win10-arm64/MRM.lib", "lib/win10-arm64/Microsoft.WindowsAppRuntime.Bootstrap.lib", "lib/win10-arm64/Microsoft.WindowsAppRuntime.lib", "lib/win10-x64/DWriteCore.lib", "lib/win10-x64/MRM.lib", "lib/win10-x64/Microsoft.WindowsAppRuntime.Bootstrap.lib", "lib/win10-x64/Microsoft.WindowsAppRuntime.lib", "lib/win10-x86/DWriteCore.lib", "lib/win10-x86/MRM.lib", "lib/win10-x86/Microsoft.WindowsAppRuntime.Bootstrap.lib", "lib/win10-x86/Microsoft.WindowsAppRuntime.lib", "license.txt", "manifests/Microsoft.InteractiveExperiences.manifest", "manifests/Microsoft.WindowsAppSdk.Foundation.manifest", "manifests/manifests/Microsoft.WindowsAppSdk.WinUI.manifest", "microsoft.windowsappsdk.1.6.240923002.nupkg.sha512", "microsoft.windowsappsdk.nuspec", "runtimes/win-arm64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll", "runtimes/win-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll", "runtimes/win-x86/native/Microsoft.WindowsAppRuntime.Bootstrap.dll", "tools/MSIX/win10-arm64/MSIX.inventory", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.1.6.msix", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.DDLM.1.6.msix", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.Main.1.6.msix", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.Singleton.1.6.msix", "tools/MSIX/win10-x64/MSIX.inventory", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.1.6.msix", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.DDLM.1.6.msix", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.Main.1.6.msix", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.Singleton.1.6.msix", "tools/MSIX/win10-x86/MSIX.inventory", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.1.6.msix", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.DDLM.1.6.msix", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.Main.1.6.msix", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.Singleton.1.6.msix", "tools/NOTICE.txt", "tools/arm64/GenXbf.dll", "tools/net472/Microsoft.Bcl.AsyncInterfaces.dll", "tools/net472/Microsoft.Build.Framework.dll", "tools/net472/Microsoft.Build.Msix.dll", "tools/net472/Microsoft.Build.Utilities.Core.dll", "tools/net472/Microsoft.Build.dll", "tools/net472/Microsoft.Cci.dll", "tools/net472/Microsoft.UI.Xaml.Markup.Compiler.IO.dll", "tools/net472/Microsoft.UI.Xaml.Markup.Compiler.MSBuildInterop.dll", "tools/net472/Microsoft.UI.Xaml.Markup.Compiler.dll", "tools/net472/Microsoft.VisualStudio.RemoteControl.dll", "tools/net472/Microsoft.VisualStudio.Telemetry.dll", "tools/net472/Microsoft.VisualStudio.Utilities.Internal.dll", "tools/net472/Newtonsoft.Json.dll", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net472/System.Text.Encodings.Web.dll", "tools/net472/System.Text.Json.dll", "tools/net472/System.Threading.Tasks.Dataflow.dll", "tools/net472/System.Threading.Tasks.Extensions.dll", "tools/net472/XamlCompiler.exe", "tools/net472/XamlCompiler.exe.config", "tools/net6.0/Microsoft.Bcl.AsyncInterfaces.dll", "tools/net6.0/Microsoft.Build.Msix.dll", "tools/net6.0/Microsoft.Cci.dll", "tools/net6.0/Microsoft.UI.Xaml.Markup.Compiler.IO.dll", "tools/net6.0/Microsoft.UI.Xaml.Markup.Compiler.MSBuildInterop.dll", "tools/net6.0/Microsoft.UI.Xaml.Markup.Compiler.dll", "tools/net6.0/Microsoft.VisualStudio.RemoteControl.dll", "tools/net6.0/Microsoft.VisualStudio.Telemetry.dll", "tools/net6.0/Microsoft.VisualStudio.Utilities.Internal.dll", "tools/net6.0/Newtonsoft.Json.dll", "tools/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "tools/net6.0/System.Text.Encodings.Web.dll", "tools/net6.0/System.Text.Json.dll", "tools/x64/GenXbf.dll", "tools/x86/GenXbf.dll"]}, "SkiaSharp/2.88.8": {"sha512": "bRkp3uKp5ZI8gXYQT57uKwil1uobb2p8c69n7v5evlB/2JNcMAXVcw9DZAP5Ig3WSvgzGm2YSn27UVeOi05NlA==", "type": "package", "path": "skiasharp/2.88.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "interactive-extensions/dotnet/SkiaSharp.DotNet.Interactive.dll", "lib/monoandroid1.0/SkiaSharp.dll", "lib/monoandroid1.0/SkiaSharp.pdb", "lib/monoandroid1.0/SkiaSharp.xml", "lib/net462/SkiaSharp.dll", "lib/net462/SkiaSharp.pdb", "lib/net462/SkiaSharp.xml", "lib/net6.0-android30.0/SkiaSharp.dll", "lib/net6.0-android30.0/SkiaSharp.pdb", "lib/net6.0-android30.0/SkiaSharp.xml", "lib/net6.0-ios13.6/SkiaSharp.dll", "lib/net6.0-ios13.6/SkiaSharp.pdb", "lib/net6.0-ios13.6/SkiaSharp.xml", "lib/net6.0-maccatalyst13.5/SkiaSharp.dll", "lib/net6.0-maccatalyst13.5/SkiaSharp.pdb", "lib/net6.0-maccatalyst13.5/SkiaSharp.xml", "lib/net6.0-macos10.15/SkiaSharp.dll", "lib/net6.0-macos10.15/SkiaSharp.pdb", "lib/net6.0-macos10.15/SkiaSharp.xml", "lib/net6.0-tizen7.0/SkiaSharp.dll", "lib/net6.0-tizen7.0/SkiaSharp.pdb", "lib/net6.0-tizen7.0/SkiaSharp.xml", "lib/net6.0-tvos13.4/SkiaSharp.dll", "lib/net6.0-tvos13.4/SkiaSharp.pdb", "lib/net6.0-tvos13.4/SkiaSharp.xml", "lib/net6.0/SkiaSharp.dll", "lib/net6.0/SkiaSharp.pdb", "lib/net6.0/SkiaSharp.xml", "lib/netcoreapp3.1/SkiaSharp.dll", "lib/netcoreapp3.1/SkiaSharp.pdb", "lib/netcoreapp3.1/SkiaSharp.xml", "lib/netstandard1.3/SkiaSharp.dll", "lib/netstandard1.3/SkiaSharp.pdb", "lib/netstandard1.3/SkiaSharp.xml", "lib/netstandard2.0/SkiaSharp.dll", "lib/netstandard2.0/SkiaSharp.pdb", "lib/netstandard2.0/SkiaSharp.xml", "lib/netstandard2.1/SkiaSharp.dll", "lib/netstandard2.1/SkiaSharp.pdb", "lib/netstandard2.1/SkiaSharp.xml", "lib/tizen40/SkiaSharp.dll", "lib/tizen40/SkiaSharp.pdb", "lib/tizen40/SkiaSharp.xml", "lib/uap10.0.10240/SkiaSharp.dll", "lib/uap10.0.10240/SkiaSharp.pdb", "lib/uap10.0.10240/SkiaSharp.xml", "lib/uap10.0.16299/SkiaSharp.dll", "lib/uap10.0.16299/SkiaSharp.pdb", "lib/uap10.0.16299/SkiaSharp.xml", "lib/xamarinios1.0/SkiaSharp.dll", "lib/xamarinios1.0/SkiaSharp.pdb", "lib/xamarinios1.0/SkiaSharp.xml", "lib/xamarinmac2.0/SkiaSharp.dll", "lib/xamarinmac2.0/SkiaSharp.pdb", "lib/xamarinmac2.0/SkiaSharp.xml", "lib/xamarintvos1.0/SkiaSharp.dll", "lib/xamarintvos1.0/SkiaSharp.pdb", "lib/xamarintvos1.0/SkiaSharp.xml", "lib/xamarinwatchos1.0/SkiaSharp.dll", "lib/xamarinwatchos1.0/SkiaSharp.pdb", "lib/xamarinwatchos1.0/SkiaSharp.xml", "skiasharp.2.88.8.nupkg.sha512", "skiasharp.nuspec"]}, "SkiaSharp.NativeAssets.macOS/2.88.8": {"sha512": "6Kn5TSkKlfyS6azWHF3Jk2sW5C4jCE5uSshM/5AbfFrR+5n6qM5XEnz9h4VaVl7LTxBvHvMkuPb/3bpbq0vxTw==", "type": "package", "path": "skiasharp.nativeassets.macos/2.88.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.macOS.targets", "build/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "build/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "lib/net462/_._", "lib/net6.0-macos10.15/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "lib/xamarinmac2.0/_._", "runtimes/osx/native/libSkiaSharp.dylib", "skiasharp.nativeassets.macos.2.88.8.nupkg.sha512", "skiasharp.nativeassets.macos.nuspec"]}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"sha512": "O9QXoWEXA+6cweR4h3BOnwMz+pO9vL9mXdjLrpDd0w1QzCgWmLQBxa1VgySDITiH7nQndrDG1h6937zm9pLj1Q==", "type": "package", "path": "skiasharp.nativeassets.win32/2.88.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Win32.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/win-arm64/native/libSkiaSharp.dll", "runtimes/win-x64/native/libSkiaSharp.dll", "runtimes/win-x86/native/libSkiaSharp.dll", "skiasharp.nativeassets.win32.2.88.8.nupkg.sha512", "skiasharp.nativeassets.win32.nuspec"]}, "SkiaSharp.Views.WinUI/2.88.8": {"sha512": "Zg+EgdDeAn8E8kCdP7WpBkOvdUHLHzmekluXLg5d4FSP0jrBEBs0uWc+dvf3A+OKlewJJzBJQSR6+mUuZe9YFg==", "type": "package", "path": "skiasharp.views.winui/2.88.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net6.0-windows10.0.18362.0/SkiaSharp.Views.Windows.dll", "lib/net6.0-windows10.0.18362.0/SkiaSharp.Views.Windows.pdb", "lib/net6.0-windows10.0.18362.0/SkiaSharp.Views.Windows.xml", "lib/net6.0-windows10.0.19041.0/SkiaSharp.Views.Windows.dll", "lib/net6.0-windows10.0.19041.0/SkiaSharp.Views.Windows.pdb", "lib/net6.0-windows10.0.19041.0/SkiaSharp.Views.Windows.xml", "skiasharp.views.winui.2.88.8.nupkg.sha512", "skiasharp.views.winui.nuspec"]}, "System.Drawing.Common/9.0.0": {"sha512": "uoozjI3+dlgKh2onFJcz8aNLh6TRCPlLSh8Dbuljc8CdvqXrxHOVysJlrHvlsOCqceqGBR1wrMPxlnzzhynktw==", "type": "package", "path": "system.drawing.common/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Drawing.Common.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Drawing.Common.dll", "lib/net462/System.Drawing.Common.pdb", "lib/net462/System.Drawing.Common.xml", "lib/net8.0/System.Drawing.Common.dll", "lib/net8.0/System.Drawing.Common.pdb", "lib/net8.0/System.Drawing.Common.xml", "lib/net8.0/System.Private.Windows.Core.dll", "lib/net8.0/System.Private.Windows.Core.xml", "lib/net9.0/System.Drawing.Common.dll", "lib/net9.0/System.Drawing.Common.pdb", "lib/net9.0/System.Drawing.Common.xml", "lib/net9.0/System.Private.Windows.Core.dll", "lib/net9.0/System.Private.Windows.Core.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.pdb", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.drawing.common.9.0.0.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/9.0.0": {"sha512": "js7+qAu/9mQvnhA4EfGMZNEzXtJCDxgkgj8ohuxq/Qxv+R56G+ljefhiJHOxTNiw54q8vmABCWUwkMulNdlZ4A==", "type": "package", "path": "system.text.json/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net8.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/net9.0/System.Text.Json.dll", "lib/net9.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.9.0.0.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "ZXing.Net/0.16.9": {"sha512": "7WaVMHklpT3Ye2ragqRIwlFRsb6kOk63BOGADV0fan3ulVfGLUYkDi5yNUsZS/7FVNkWbtHAlDLmu4WnHGfqvQ==", "type": "package", "path": "zxing.net/0.16.9", "files": [".nupkg.metadata", ".signature.p7s", "lib/native/zxing.XML", "lib/native/zxing.pri", "lib/native/zxing.winmd", "lib/net20-cf/zxing.ce2.0.dll", "lib/net20-cf/zxing.ce2.0.xml", "lib/net20/zxing.XML", "lib/net20/zxing.dll", "lib/net35-cf/zxing.ce3.5.dll", "lib/net35-cf/zxing.ce3.5.xml", "lib/net35/zxing.XML", "lib/net35/zxing.dll", "lib/net40/zxing.XML", "lib/net40/zxing.dll", "lib/net40/zxing.presentation.XML", "lib/net40/zxing.presentation.dll", "lib/net45/zxing.XML", "lib/net45/zxing.dll", "lib/net45/zxing.presentation.XML", "lib/net45/zxing.presentation.dll", "lib/net461/zxing.XML", "lib/net461/zxing.dll", "lib/net461/zxing.presentation.XML", "lib/net461/zxing.presentation.dll", "lib/net47/zxing.XML", "lib/net47/zxing.dll", "lib/net47/zxing.presentation.XML", "lib/net47/zxing.presentation.dll", "lib/net48/zxing.XML", "lib/net48/zxing.dll", "lib/net48/zxing.presentation.XML", "lib/net48/zxing.presentation.dll", "lib/net5.0/zxing.XML", "lib/net5.0/zxing.dll", "lib/net6.0/zxing.XML", "lib/net6.0/zxing.dll", "lib/net7.0/zxing.XML", "lib/net7.0/zxing.dll", "lib/netcoreapp3.0/zxing.dll", "lib/netcoreapp3.0/zxing.xml", "lib/netcoreapp3.1/zxing.dll", "lib/netcoreapp3.1/zxing.xml", "lib/netstandard1.0/zxing.dll", "lib/netstandard1.0/zxing.xml", "lib/netstandard1.1/zxing.dll", "lib/netstandard1.1/zxing.xml", "lib/netstandard1.3/zxing.dll", "lib/netstandard1.3/zxing.xml", "lib/netstandard2.0/zxing.dll", "lib/netstandard2.0/zxing.xml", "lib/netstandard2.1/zxing.dll", "lib/netstandard2.1/zxing.xml", "lib/portable-win+net40+sl4+sl5+wp7+wp71+wp8/zxing.portable.XML", "lib/portable-win+net40+sl4+sl5+wp7+wp71+wp8/zxing.portable.dll", "lib/sl3-wp/zxing.wp7.0.XML", "lib/sl3-wp/zxing.wp7.0.dll", "lib/sl4-wp71/zxing.wp7.1.XML", "lib/sl4-wp71/zxing.wp7.1.dll", "lib/sl4/zxing.sl4.XML", "lib/sl4/zxing.sl4.dll", "lib/sl5/zxing.sl5.XML", "lib/sl5/zxing.sl5.dll", "lib/uap10/zxing.dll", "lib/uap10/zxing.pri", "lib/uap10/zxing.xml", "lib/windows8-managed/zxing.winrt.XML", "lib/windows8-managed/zxing.winrt.dll", "lib/windows8/zxing.XML", "lib/windows8/zxing.pri", "lib/windows8/zxing.winmd", "lib/wp8/zxing.wp8.0.XML", "lib/wp8/zxing.wp8.0.dll", "logo.jpg", "zxing.net.0.16.9.nupkg.sha512", "zxing.net.nuspec"]}}, "projectFileDependencyGroups": {"net9.0-windows10.0.19041": ["Microsoft.Extensions.Logging.Debug >= 9.0.0", "Microsoft.Maui.Controls >= 9.0.10", "Microsoft.Maui.Controls.Compatibility >= 9.0.10", "Microsoft.Maui.Graphics.Skia >= 9.0.10", "System.Drawing.Common >= 9.0.0", "System.Text.Json >= 9.0.0", "ZXing.Net >= 0.16.9"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\united2026\\unitedprint\\unitedprint\\unitedprint.csproj", "projectName": "unitedprint", "projectPath": "C:\\Users\\<USER>\\Desktop\\united2026\\unitedprint\\unitedprint\\unitedprint.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\united2026\\unitedprint\\unitedprint\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows10.0.19041.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows10.0.19041": {"targetAlias": "net9.0-windows10.0.19041.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows10.0.19041": {"targetAlias": "net9.0-windows10.0.19041.0", "dependencies": {"Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.10, )"}, "Microsoft.Maui.Controls.Compatibility": {"target": "Package", "version": "[9.0.10, )"}, "Microsoft.Maui.Graphics.Skia": {"target": "Package", "version": "[9.0.10, )"}, "System.Drawing.Common": {"target": "Package", "version": "[9.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.0, )"}, "ZXing.Net": {"target": "Package", "version": "[0.16.9, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.Windows.SDK.NET.Ref", "version": "[10.0.19041.57, 10.0.19041.57]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.Windows.SDK.NET.Ref.Windows": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win10-x64": {"#import": []}}}}