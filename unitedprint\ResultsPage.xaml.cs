using Microsoft.Maui.Controls;
using System.Text.Json;

namespace unitedprint
{
    public partial class ResultsPage : ContentPage
    {
        private JsonElement _registrationData;
        private string _phoneNumber;

        public ResultsPage()
        {
            InitializeComponent();
        }

        public ResultsPage(JsonElement registrationData, string phoneNumber) : this()
        {
            _registrationData = registrationData;
            _phoneNumber = phoneNumber;
            LoadRegistrationData();
        }

        private void LoadRegistrationData()
        {
            try
            {
                // Extract student name
                string studentName = "Unknown Student";
                if (_registrationData.TryGetProperty("name", out var nameElement))
                {
                    studentName = nameElement.GetString() ?? "Unknown Student";
                }
                else if (_registrationData.TryGetProperty("student_name", out var studentNameElement))
                {
                    studentName = studentNameElement.GetString() ?? "Unknown Student";
                }
                else if (_registrationData.TryGetProperty("full_name", out var fullNameElement))
                {
                    studentName = fullNameElement.GetString() ?? "Unknown Student";
                }

                // Set the labels
                StudentNameLabel.Text = studentName;
                PhoneNumberLabel.Text = _phoneNumber;
            }
            catch (Exception ex)
            {
                // Fallback in case of any issues
                StudentNameLabel.Text = "Registration Found";
                PhoneNumberLabel.Text = _phoneNumber;
            }
        }

        private async void OnAttendeeNumberClicked(object sender, EventArgs e)
        {
            if (sender is Button button)
            {
                int attendeeCount = int.Parse(button.Text);
                
                // Show confirmation
                bool confirm = await DisplayAlert(
                    "Confirm Selection", 
                    $"You selected {attendeeCount} attendee(s) for {StudentNameLabel.Text}.\n\nProceed with printing?", 
                    "Yes, Print", 
                    "Cancel"
                );

                if (confirm)
                {
                    // Here you can add printing logic or navigate to a printing page
                    await ProcessPrintRequest(attendeeCount);
                }
            }
        }

        private async Task ProcessPrintRequest(int attendeeCount)
        {
            try
            {
                // Show processing message
                await DisplayAlert(
                    "Processing", 
                    $"Processing print request for {attendeeCount} attendee(s)...\n\nStudent: {StudentNameLabel.Text}\nPhone: {PhoneNumberLabel.Text}", 
                    "OK"
                );

                // Here you would typically:
                // 1. Send print request to printer
                // 2. Update database with attendance count
                // 3. Generate receipt/confirmation
                
                // For now, just show success message
                await DisplayAlert(
                    "Success", 
                    $"Print request completed successfully!\n\nAttendees: {attendeeCount}\nStudent: {StudentNameLabel.Text}", 
                    "OK"
                );

                // Navigate back to main page
                await Shell.Current.GoToAsync("..");
            }
            catch (Exception ex)
            {
                await DisplayAlert("Error", $"Print request failed: {ex.Message}", "OK");
            }
        }

        private async void OnBackToHomeClicked(object sender, EventArgs e)
        {
            // Navigate back to the main page
            await Shell.Current.GoToAsync("..");
        }
    }
}
